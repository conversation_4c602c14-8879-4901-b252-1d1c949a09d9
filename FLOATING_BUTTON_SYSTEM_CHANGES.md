# Floating Button System - Cập nhật mới

## Tổng quan thay đổi

Đã chỉnh sửa lại floating system để các button được hiển thị theo thứ tự ưu tiên thay vì fix cứng vị trí trong screen. Thiết kế lại floating button wrapper để hiển thị các button theo flex vertical layout.

## Files đã thay đổi

### 1. `src/components/ui/float-button.tsx`
- **Thêm**: `priority` field cho FloatButtonProps
- **Cập nhật**: FloatButton và FloatMenuButton sử dụng FloatingButtonWrapper mới
- **Thêm**: Sắp xếp items theo priority (cao → thấp)
- **Xóa**: FloatingButtonWrapper cũ (chuyển sang floating-button-manager)

### 2. `src/hooks/use-floating-button-system.ts` (Mới)
- **Hook chính**: `useFloatingButtonSystem` - quản lý toàn bộ hệ thống floating button
- **Hook phụ**: `useFloatingButton` - quản lý button riêng lẻ
- **Tính năng**:
  - Priority-based ordering
  - Dynamic button management (add/remove/show/hide)
  - Position calculation cho vertical stack
  - Integration với floating UI system

### 3. `src/components/floating-ui/floating-button-manager.tsx` (Mới)
- **FloatingButtonManager**: Component quản lý toàn bộ hệ thống
- **FloatingButtonStack**: Component hiển thị stack button với system integration
- **FloatingButtonWrapper**: Component wrapper cập nhật với flex vertical layout
- **FloatingButtonRenderer**: Internal component render button qua floating UI

### 4. `src/components/floating-ui/index.ts`
- **Thêm exports**: Các component và hook mới từ floating-button-manager

### 5. `docs/floating-button-system.md` (Mới)
- **Documentation**: Hướng dẫn sử dụng hệ thống mới
- **Examples**: Các ví dụ sử dụng components và hooks
- **Migration guide**: Hướng dẫn chuyển đổi từ hệ thống cũ

### 6. `src/components/examples/floating-button-demo.tsx` (Mới)
- **Demo components**: Minh họa cách sử dụng hệ thống mới
- **Examples**: FloatingButtonDemo, FloatingButtonStackDemo, IndividualButtonDemo

## Tính năng mới

### 1. Priority-based Ordering
```tsx
const items = [
  { label: 'Settings', priority: 10 }, // Hiển thị đầu tiên
  { label: 'Info', priority: 5 },     // Hiển thị thứ hai
  { label: 'Help', priority: 1 },     // Hiển thị cuối cùng
];
```

### 2. Flex Vertical Layout
- Tự động sắp xếp button theo chiều dọc
- Hỗ trợ 4 vị trí: bottom-right, bottom-left, top-right, top-left
- Tự động điều chỉnh flex-direction dựa trên position
- Custom gap giữa các button

### 3. Dynamic Management
```tsx
const system = useFloatingButtonSystem('my-system');

// Thêm button
system.registerButton({
  id: 'settings',
  label: 'Settings',
  icon: <Settings />,
  onClick: () => {},
  priority: 10,
});

// Điều khiển button
system.showButton('settings');
system.hideButton('settings');
system.showAllButtons();
system.hideAllButtons();
```

### 4. Integration với Floating UI
- Collision detection
- Animation effects
- Z-index management
- Responsive positioning

## Cách sử dụng

### Basic Usage
```tsx
import { FloatButton } from '@/components/ui';

<FloatButton
  items={[
    {
      label: 'Settings',
      icon: <Settings />,
      onClick: () => {},
      priority: 10, // Cao nhất → hiển thị đầu tiên
    },
    {
      label: 'Info',
      icon: <Info />,
      onClick: () => {},
      priority: 5,
    },
  ]}
/>
```

### Advanced Usage
```tsx
import { FloatingButtonManager, useFloatingButtonSystem } from '@/components/floating-ui';

// Trong component
const system = useFloatingButtonSystem('main-system', {
  position: 'bottom-right',
  gap: 3,
  maxVisible: 5,
});

// Register buttons
React.useEffect(() => {
  system.registerButton({
    id: 'settings',
    label: 'Settings',
    icon: <Settings />,
    onClick: () => {},
    priority: 10,
  });
}, []);

// Render manager
<FloatingButtonManager
  systemId="main-system"
  position="bottom-right"
  gap={3}
  maxVisible={5}
/>
```

### Manual Stack
```tsx
import { FloatingButtonStack } from '@/components/floating-ui';

<FloatingButtonStack position="bottom-left" gap={2}>
  <Button>Button 1</Button>
  <Button>Button 2</Button>
  <Button>Main Button</Button>
</FloatingButtonStack>
```

## Backward Compatibility

- Các component FloatButton và FloatMenuButton vẫn hoạt động như cũ
- Chỉ cần thêm `priority` field để sử dụng tính năng mới
- FloatingButtonWrapper cũ vẫn có thể sử dụng thông qua import mới

## Migration

### Từ fixed positioning
```tsx
// Cũ
<div className="fixed bottom-6 right-6 z-50 flex flex-col items-end gap-2">
  <Button>Button 1</Button>
  <Button>Button 2</Button>
</div>

// Mới
<FloatingButtonWrapper position="bottom-right" gap={2}>
  <Button>Button 1</Button>
  <Button>Button 2</Button>
</FloatingButtonWrapper>
```

### Thêm priority cho existing buttons
```tsx
// Cũ
const items = [
  { label: 'Settings', icon: <Settings />, onClick: () => {} },
  { label: 'Info', icon: <Info />, onClick: () => {} },
];

// Mới
const items = [
  { label: 'Settings', icon: <Settings />, onClick: () => {}, priority: 10 },
  { label: 'Info', icon: <Info />, onClick: () => {}, priority: 5 },
];
```

## Testing

Để test hệ thống mới:

1. Import demo component:
```tsx
import { FloatingButtonDemo } from '@/components/examples/floating-button-demo';
```

2. Sử dụng trong page:
```tsx
<FloatingButtonDemo />
```

3. Kiểm tra các tính năng:
   - Priority ordering
   - Show/hide buttons
   - Position changes
   - Collision detection
