const { chromium } = require('playwright');

async function debugConsoleErrors() {
  console.log('🔍 Debugging console errors...');
  
  const browser = await chromium.launch({ headless: false });
  const context = await browser.newContext({
    viewport: { width: 1280, height: 720 }
  });
  const page = await context.newPage();

  // Listen for console messages
  const consoleMessages = [];
  page.on('console', msg => {
    const type = msg.type();
    const text = msg.text();
    
    consoleMessages.push({ type, text, timestamp: Date.now() });
    
    if (type === 'error') {
      console.log(`❌ Console Error: ${text}`);
    } else if (type === 'warning') {
      console.log(`⚠️  Console Warning: ${text}`);
    } else if (type === 'log' && text.includes('IntegratedFloatingButtons')) {
      console.log(`📝 Console Log: ${text}`);
    }
  });

  // Listen for page errors
  page.on('pageerror', error => {
    console.log(`💥 Page Error: ${error.message}`);
    console.log(`Stack: ${error.stack}`);
  });

  try {
    console.log('📍 Navigating to localhost:3000...');
    await page.goto('http://localhost:3000', { waitUntil: 'networkidle' });
    await page.waitForTimeout(5000);

    // Check for React errors
    const reactErrors = consoleMessages.filter(msg => 
      msg.type === 'error' && 
      (msg.text.includes('Maximum update depth') || 
       msg.text.includes('IntegratedFloatingButtons') ||
       msg.text.includes('useEffect'))
    );

    console.log(`\n📊 React Errors Found: ${reactErrors.length}`);
    reactErrors.forEach((error, index) => {
      console.log(`${index + 1}. ${error.text}`);
    });

    // Check if IntegratedFloatingButtons is trying to render
    const componentLogs = consoleMessages.filter(msg => 
      msg.text.includes('IntegratedFloatingButtons') ||
      msg.text.includes('FloatingButtonManager') ||
      msg.text.includes('registerButton')
    );

    console.log(`\n📊 Component Logs: ${componentLogs.length}`);
    componentLogs.forEach((log, index) => {
      console.log(`${index + 1}. [${log.type}] ${log.text}`);
    });

    // Check DOM structure
    const domInfo = await page.evaluate(() => {
      return {
        hasFloatingUIProvider: document.querySelector('[data-floating-ui-provider]') !== null,
        hasFloatingButtonManager: document.querySelector('.floating-button-manager') !== null,
        hasFixedElements: document.querySelectorAll('.fixed').length,
        totalButtons: document.querySelectorAll('button').length,
        bodyChildren: document.body.children.length,
        reactRoot: document.querySelector('#__next') !== null
      };
    });

    console.log('\n📊 DOM Information:');
    Object.entries(domInfo).forEach(([key, value]) => {
      console.log(`${key}: ${value}`);
    });

    // Try to add debug logging to IntegratedFloatingButtons
    await page.evaluate(() => {
      // Add debug logging
      console.log('🔍 Debug: Checking for IntegratedFloatingButtons in DOM');
      
      // Look for any elements that might be from IntegratedFloatingButtons
      const possibleElements = document.querySelectorAll('*');
      let foundElements = 0;
      
      possibleElements.forEach(el => {
        if (el.className && (
          el.className.includes('floating') ||
          el.className.includes('integrated') ||
          el.className.includes('button-manager')
        )) {
          console.log('🔍 Found possible element:', el.tagName, el.className);
          foundElements++;
        }
      });
      
      console.log(`🔍 Total possible elements found: ${foundElements}`);
    });

    // Wait a bit more and check again
    await page.waitForTimeout(3000);

    const finalCheck = await page.evaluate(() => {
      return {
        buttons: document.querySelectorAll('button').length,
        fixedElements: document.querySelectorAll('.fixed').length,
        floatingElements: document.querySelectorAll('[class*="floating"]').length
      };
    });

    console.log('\n📊 Final Check:');
    Object.entries(finalCheck).forEach(([key, value]) => {
      console.log(`${key}: ${value}`);
    });

    // Summary
    console.log('\n📋 SUMMARY:');
    const hasMaxUpdateError = reactErrors.some(e => e.text.includes('Maximum update depth'));
    const hasTranslationErrors = consoleMessages.some(msg => msg.text.includes('Translation key not found'));
    
    if (hasMaxUpdateError) {
      console.log('❌ Maximum update depth error detected - infinite re-render');
      console.log('🔧 Need to fix useEffect dependencies in IntegratedFloatingButtons');
    }
    
    if (hasTranslationErrors) {
      console.log('⚠️  Translation errors detected - may cause rendering issues');
    }
    
    if (finalCheck.buttons === 0) {
      console.log('❌ No buttons found - IntegratedFloatingButtons not rendering');
    } else {
      console.log(`✅ Found ${finalCheck.buttons} buttons`);
    }

    // Take screenshot
    await page.screenshot({ path: 'console-debug-result.png', fullPage: true });
    console.log('\n📸 Screenshot saved as console-debug-result.png');

  } catch (error) {
    console.error('❌ Debug failed:', error);
  } finally {
    await browser.close();
    console.log('\n✅ Console debug completed!');
  }
}

// Run the debug
debugConsoleErrors().catch(console.error);
