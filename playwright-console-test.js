const { chromium } = require('playwright');

async function checkConsoleErrors() {
  console.log('🔍 Checking console errors...');
  
  const browser = await chromium.launch({ headless: false });
  const context = await browser.newContext({
    viewport: { width: 1280, height: 720 }
  });
  const page = await context.newPage();

  // Listen for console messages
  page.on('console', msg => {
    const type = msg.type();
    const text = msg.text();
    
    if (type === 'error') {
      console.log(`❌ Console Error: ${text}`);
    } else if (type === 'warning') {
      console.log(`⚠️  Console Warning: ${text}`);
    } else if (type === 'log') {
      console.log(`📝 Console Log: ${text}`);
    }
  });

  // Listen for page errors
  page.on('pageerror', error => {
    console.log(`💥 Page Error: ${error.message}`);
  });

  try {
    console.log('📍 Navigating to localhost:3000...');
    await page.goto('http://localhost:3000', { waitUntil: 'networkidle' });
    await page.waitForTimeout(5000);

    // Check if React is loaded
    const reactInfo = await page.evaluate(() => {
      return {
        hasReact: typeof window.React !== 'undefined',
        hasReactDOM: typeof window.ReactDOM !== 'undefined',
        reactVersion: window.React?.version || 'not found'
      };
    });
    
    console.log('\n📊 React Info:', reactInfo);

    // Check if IntegratedFloatingButtons component exists
    const componentCheck = await page.evaluate(() => {
      // Look for any elements that might indicate the component is trying to render
      const checks = {
        hasIntegratedClass: document.querySelector('[class*="integrated"]') !== null,
        hasFloatingClass: document.querySelector('[class*="floating"]') !== null,
        hasFixedElements: document.querySelectorAll('.fixed').length,
        hasZIndexElements: document.querySelectorAll('[style*="z-index"]').length,
        totalButtons: document.querySelectorAll('button').length,
        bodyChildren: document.body.children.length,
        hasNextRoot: document.querySelector('#__next') !== null,
        hasReactRoot: document.querySelector('[data-reactroot]') !== null
      };
      
      return checks;
    });
    
    console.log('\n📊 Component Check:', componentCheck);

    // Check DOM structure
    const domStructure = await page.evaluate(() => {
      const getElementInfo = (el, depth = 0) => {
        if (depth > 3) return '...';
        
        const children = Array.from(el.children).map(child => 
          `${'  '.repeat(depth + 1)}${child.tagName}${child.className ? `.${child.className.split(' ').join('.')}` : ''}${child.id ? `#${child.id}` : ''}`
        ).join('\n');
        
        return children;
      };
      
      return {
        bodyStructure: getElementInfo(document.body),
        mainElements: Array.from(document.body.children).map(el => ({
          tagName: el.tagName,
          className: el.className,
          id: el.id,
          childrenCount: el.children.length
        }))
      };
    });
    
    console.log('\n📊 DOM Structure:');
    console.log(domStructure.bodyStructure);
    
    console.log('\n📊 Main Elements:');
    domStructure.mainElements.forEach((el, index) => {
      console.log(`${index + 1}. ${el.tagName}${el.className ? `.${el.className}` : ''}${el.id ? `#${el.id}` : ''} (${el.childrenCount} children)`);
    });

    // Take screenshot
    await page.screenshot({ path: 'console-debug.png', fullPage: true });
    console.log('\n📸 Screenshot saved as console-debug.png');

  } catch (error) {
    console.error('❌ Test failed:', error);
  } finally {
    await browser.close();
    console.log('\n✅ Console check completed!');
  }
}

// Run the test
checkConsoleErrors().catch(console.error);
