const { chromium } = require('playwright');

async function debugFloatingButtons() {
  console.log('🔍 Starting detailed debug test...');
  
  const browser = await chromium.launch({ headless: false });
  const context = await browser.newContext({
    viewport: { width: 1280, height: 720 }
  });
  const page = await context.newPage();

  try {
    // Navigate to localhost:3000
    console.log('📍 Navigating to localhost:3000...');
    await page.goto('http://localhost:3000', { waitUntil: 'networkidle' });
    await page.waitForTimeout(3000);

    // Debug: Check all floating elements
    console.log('\n🔍 Checking all floating elements...');
    
    const floatingInfo = await page.evaluate(() => {
      const results = [];
      
      // Find all elements with position fixed
      const fixedElements = document.querySelectorAll('*');
      
      fixedElements.forEach((el) => {
        const styles = window.getComputedStyle(el);
        if (styles.position === 'fixed') {
          const rect = el.getBoundingClientRect();
          
          results.push({
            tagName: el.tagName,
            className: el.className,
            id: el.id,
            position: {
              x: rect.x,
              y: rect.y,
              width: rect.width,
              height: rect.height
            },
            styles: {
              bottom: styles.bottom,
              right: styles.right,
              top: styles.top,
              left: styles.left,
              zIndex: styles.zIndex
            },
            innerHTML: el.innerHTML.substring(0, 100) + '...',
            hasButton: el.tagName === 'BUTTON' || el.querySelector('button') !== null,
            ariaLabel: el.getAttribute('aria-label'),
            isVisible: styles.display !== 'none' && styles.visibility !== 'hidden'
          });
        }
      });
      
      return results;
    });

    console.log(`Found ${floatingInfo.length} fixed positioned elements:`);
    floatingInfo.forEach((el, index) => {
      console.log(`\n${index + 1}. ${el.tagName} ${el.className ? `(${el.className})` : ''}`);
      console.log(`   ID: ${el.id || 'none'}`);
      console.log(`   Position: x=${el.position.x}, y=${el.position.y}`);
      console.log(`   Size: ${el.position.width}x${el.position.height}`);
      console.log(`   CSS Position: bottom=${el.styles.bottom}, right=${el.styles.right}, top=${el.styles.top}, left=${el.styles.left}`);
      console.log(`   Z-index: ${el.styles.zIndex}`);
      console.log(`   Has button: ${el.hasButton}`);
      console.log(`   Visible: ${el.isVisible}`);
      console.log(`   ARIA label: ${el.ariaLabel || 'none'}`);
      console.log(`   Content: ${el.innerHTML.substring(0, 50)}...`);
    });

    // Look specifically for IntegratedFloatingButtons
    console.log('\n🔍 Looking for IntegratedFloatingButtons...');
    
    const integratedButtons = await page.evaluate(() => {
      // Look for elements that might be from IntegratedFloatingButtons
      const selectors = [
        '.fixed.z-50.flex',
        '[class*="floating-button"]',
        '.fixed button',
        'button[aria-label*="Settings"]',
        'button[aria-label*="Feedback"]'
      ];
      
      const results = [];
      
      selectors.forEach(selector => {
        const elements = document.querySelectorAll(selector);
        elements.forEach(el => {
          const rect = el.getBoundingClientRect();
          const styles = window.getComputedStyle(el);
          
          results.push({
            selector,
            tagName: el.tagName,
            className: el.className,
            position: {
              x: rect.x,
              y: rect.y,
              width: rect.width,
              height: rect.height
            },
            styles: {
              position: styles.position,
              bottom: styles.bottom,
              right: styles.right,
              zIndex: styles.zIndex
            },
            ariaLabel: el.getAttribute('aria-label'),
            parentClassName: el.parentElement?.className || 'none'
          });
        });
      });
      
      return results;
    });

    console.log(`Found ${integratedButtons.length} potential IntegratedFloatingButtons elements:`);
    integratedButtons.forEach((el, index) => {
      console.log(`\n${index + 1}. ${el.tagName} found by selector: ${el.selector}`);
      console.log(`   Class: ${el.className}`);
      console.log(`   Parent class: ${el.parentClassName}`);
      console.log(`   Position: x=${el.position.x}, y=${el.position.y}`);
      console.log(`   CSS: position=${el.styles.position}, bottom=${el.styles.bottom}, right=${el.styles.right}`);
      console.log(`   ARIA label: ${el.ariaLabel || 'none'}`);
    });

    // Check if FloatingButtonManager is working
    console.log('\n🔍 Checking FloatingButtonManager...');
    
    const managerInfo = await page.evaluate(() => {
      // Look for elements with FloatingButtonManager classes
      const managers = document.querySelectorAll('[class*="floating-button-manager"], .fixed.z-50.flex');
      const results = [];
      
      managers.forEach(manager => {
        const rect = manager.getBoundingClientRect();
        const styles = window.getComputedStyle(manager);
        const buttons = manager.querySelectorAll('button');
        
        results.push({
          className: manager.className,
          position: {
            x: rect.x,
            y: rect.y,
            width: rect.width,
            height: rect.height
          },
          styles: {
            position: styles.position,
            bottom: styles.bottom,
            right: styles.right,
            flexDirection: styles.flexDirection,
            gap: styles.gap
          },
          buttonCount: buttons.length,
          buttons: Array.from(buttons).map(btn => ({
            ariaLabel: btn.getAttribute('aria-label'),
            className: btn.className,
            rect: btn.getBoundingClientRect()
          }))
        });
      });
      
      return results;
    });

    console.log(`Found ${managerInfo.length} FloatingButtonManager elements:`);
    managerInfo.forEach((manager, index) => {
      console.log(`\n${index + 1}. Manager:`);
      console.log(`   Class: ${manager.className}`);
      console.log(`   Position: x=${manager.position.x}, y=${manager.position.y}`);
      console.log(`   CSS: position=${manager.styles.position}, bottom=${manager.styles.bottom}, right=${manager.styles.right}`);
      console.log(`   Flex: direction=${manager.styles.flexDirection}, gap=${manager.styles.gap}`);
      console.log(`   Button count: ${manager.buttonCount}`);
      
      manager.buttons.forEach((btn, btnIndex) => {
        console.log(`     Button ${btnIndex + 1}: ${btn.ariaLabel || 'no label'} at x=${btn.rect.x}, y=${btn.rect.y}`);
      });
    });

    // Take detailed screenshot
    await page.screenshot({ path: 'floating-debug-detailed.png', fullPage: true });
    console.log('\n📸 Detailed screenshot saved as floating-debug-detailed.png');

    // Test specific positioning
    console.log('\n🔍 Testing expected positioning...');
    
    // Check if buttons are at bottom-right
    const viewportWidth = 1280;
    const viewportHeight = 720;
    const expectedBottomDistance = 24; // 6 * 4 = 24px (bottom-6)
    const expectedRightDistance = 24;  // 6 * 4 = 24px (right-6)
    
    const bottomRightButtons = await page.evaluate((vw, vh, expectedBottom, expectedRight) => {
      const buttons = document.querySelectorAll('button');
      const results = [];
      
      buttons.forEach(btn => {
        const rect = btn.getBoundingClientRect();
        const distanceFromBottom = vh - (rect.y + rect.height);
        const distanceFromRight = vw - (rect.x + rect.width);
        
        // Check if button is near bottom-right corner
        if (distanceFromBottom < 100 && distanceFromRight < 100) {
          results.push({
            ariaLabel: btn.getAttribute('aria-label'),
            className: btn.className,
            position: { x: rect.x, y: rect.y },
            distanceFromBottom,
            distanceFromRight,
            isExpectedPosition: Math.abs(distanceFromBottom - expectedBottom) < 10 && 
                               Math.abs(distanceFromRight - expectedRight) < 10
          });
        }
      });
      
      return results;
    }, viewportWidth, viewportHeight, expectedBottomDistance, expectedRightDistance);

    console.log(`Found ${bottomRightButtons.length} buttons near bottom-right corner:`);
    bottomRightButtons.forEach((btn, index) => {
      console.log(`\n${index + 1}. ${btn.ariaLabel || 'No label'}`);
      console.log(`   Position: x=${btn.position.x}, y=${btn.position.y}`);
      console.log(`   Distance from bottom: ${btn.distanceFromBottom}px`);
      console.log(`   Distance from right: ${btn.distanceFromRight}px`);
      console.log(`   Expected position: ${btn.isExpectedPosition ? '✅ YES' : '❌ NO'}`);
    });

  } catch (error) {
    console.error('❌ Debug test failed:', error);
  } finally {
    await browser.close();
    console.log('\n✅ Debug test completed!');
  }
}

// Run the debug test
debugFloatingButtons().catch(console.error);
