const { chromium } = require('playwright');

async function testDialogFunctionality() {
  console.log('🔍 Testing dialog functionality...');
  
  const browser = await chromium.launch({ headless: false });
  const context = await browser.newContext({
    viewport: { width: 1280, height: 720 }
  });
  const page = await context.newPage();

  // Listen for dialogs (alerts)
  let dialogMessages = [];
  page.on('dialog', dialog => {
    console.log(`📢 Dialog detected: ${dialog.type()} - "${dialog.message()}"`);
    dialogMessages.push({
      type: dialog.type(),
      message: dialog.message()
    });
    dialog.accept(); // Auto-accept dialogs
  });

  try {
    console.log('📍 Navigating to localhost:3000...');
    await page.goto('http://localhost:3000', { waitUntil: 'networkidle' });
    await page.waitForTimeout(3000);

    // Check for floating buttons
    const buttons = await page.locator('button[aria-label]').count();
    console.log(`Found ${buttons} buttons with aria-label`);

    if (buttons > 0) {
      // Get all buttons with aria-label
      const buttonInfo = await page.evaluate(() => {
        const buttons = document.querySelectorAll('button[aria-label]');
        return Array.from(buttons).map(btn => ({
          ariaLabel: btn.getAttribute('aria-label'),
          rect: btn.getBoundingClientRect(),
          isVisible: btn.offsetParent !== null
        }));
      });

      console.log('\nButton information:');
      buttonInfo.forEach((btn, index) => {
        console.log(`${index + 1}. ${btn.ariaLabel} - Visible: ${btn.isVisible}`);
      });

      // Test Settings button
      console.log('\n🔍 Testing Settings button...');
      const settingsButton = page.locator('button[aria-label="Settings"]').first();
      if (await settingsButton.count() > 0) {
        console.log('Clicking Settings button...');
        await settingsButton.click();
        await page.waitForTimeout(2000);

        // Check if modal appeared
        const modals = await page.locator('[role="dialog"], .modal, .fixed.inset-0, [data-state="open"]').count();
        console.log(`Modals found after Settings click: ${modals}`);

        if (modals > 0) {
          console.log('✅ Settings modal opened successfully');
          
          // Try to close modal
          const closeButtons = await page.locator('button:has-text("×"), button[aria-label*="close"], button[aria-label*="Close"]').count();
          if (closeButtons > 0) {
            await page.locator('button:has-text("×"), button[aria-label*="close"], button[aria-label*="Close"]').first().click();
            await page.waitForTimeout(1000);
            console.log('✅ Settings modal closed');
          }
        } else {
          console.log('❌ No settings modal found');
        }
      }

      // Test Feedback button
      console.log('\n🔍 Testing Feedback button...');
      const feedbackButton = page.locator('button[aria-label="Feedback"]').first();
      if (await feedbackButton.count() > 0) {
        console.log('Clicking Feedback button...');
        await feedbackButton.click();
        await page.waitForTimeout(2000);

        // Check if modal appeared
        const modals = await page.locator('[role="dialog"], .modal, .fixed.inset-0, [data-state="open"]').count();
        console.log(`Modals found after Feedback click: ${modals}`);

        if (modals > 0) {
          console.log('✅ Feedback modal opened successfully');
          
          // Try to close modal
          const closeButtons = await page.locator('button:has-text("×"), button[aria-label*="close"], button[aria-label*="Close"]').count();
          if (closeButtons > 0) {
            await page.locator('button:has-text("×"), button[aria-label*="close"], button[aria-label*="Close"]').first().click();
            await page.waitForTimeout(1000);
            console.log('✅ Feedback modal closed');
          }
        } else {
          console.log('❌ No feedback modal found');
        }
      }
    }

    // Check what type of buttons we have
    console.log('\n🔍 Analyzing button implementation...');
    const buttonAnalysis = await page.evaluate(() => {
      const buttons = document.querySelectorAll('button[aria-label]');
      const analysis = {
        totalButtons: buttons.length,
        buttonTypes: [],
        hasSimpleIntegrated: false,
        hasIntegratedFloating: false,
        hasOldSystem: false
      };

      buttons.forEach(btn => {
        const parent = btn.closest('div');
        const parentClass = parent?.className || '';
        
        analysis.buttonTypes.push({
          ariaLabel: btn.getAttribute('aria-label'),
          parentClass: parentClass.substring(0, 100),
          hasAlert: btn.onclick?.toString().includes('alert') || false
        });

        if (parentClass.includes('fixed bottom-6 right-6')) {
          analysis.hasSimpleIntegrated = true;
        }
        if (parentClass.includes('floating-button-manager')) {
          analysis.hasIntegratedFloating = true;
        }
      });

      return analysis;
    });

    console.log('\nButton Analysis:');
    console.log(`Total buttons: ${buttonAnalysis.totalButtons}`);
    console.log(`Has SimpleIntegratedButtons: ${buttonAnalysis.hasSimpleIntegrated}`);
    console.log(`Has IntegratedFloatingButtons: ${buttonAnalysis.hasIntegratedFloating}`);
    
    buttonAnalysis.buttonTypes.forEach((btn, index) => {
      console.log(`${index + 1}. ${btn.ariaLabel}:`);
      console.log(`   Parent class: ${btn.parentClass}...`);
      console.log(`   Has alert: ${btn.hasAlert}`);
    });

    // Summary of dialog messages
    console.log('\n📋 Dialog Summary:');
    if (dialogMessages.length > 0) {
      dialogMessages.forEach((dialog, index) => {
        console.log(`${index + 1}. ${dialog.type}: "${dialog.message}"`);
      });
      console.log('⚠️  Only alerts found - no proper modals');
    } else {
      console.log('No dialogs detected');
    }

    // Take screenshot
    await page.screenshot({ path: 'dialog-test-result.png', fullPage: true });
    console.log('\n📸 Screenshot saved as dialog-test-result.png');

    // Recommendations
    console.log('\n💡 RECOMMENDATIONS:');
    if (buttonAnalysis.hasSimpleIntegrated && !buttonAnalysis.hasIntegratedFloating) {
      console.log('❌ Currently using SimpleIntegratedButtons (only alerts)');
      console.log('✅ Need to switch back to IntegratedFloatingButtons for proper modals');
      console.log('🔧 Action: Replace SimpleIntegratedButtons with IntegratedFloatingButtons in layout');
    } else if (buttonAnalysis.hasIntegratedFloating) {
      console.log('✅ Using IntegratedFloatingButtons');
      console.log('🔍 Check why modals are not appearing');
    } else {
      console.log('❓ Unknown button system detected');
    }

  } catch (error) {
    console.error('❌ Test failed:', error);
  } finally {
    await browser.close();
    console.log('\n✅ Dialog test completed!');
  }
}

// Run the test
testDialogFunctionality().catch(console.error);
