const { chromium } = require('playwright');

async function finalFloatingTest() {
  console.log('🎯 Final floating buttons test...');
  
  const browser = await chromium.launch({ headless: false });
  const context = await browser.newContext({
    viewport: { width: 1280, height: 720 }
  });
  const page = await context.newPage();

  try {
    console.log('📍 Navigating to localhost:3000...');
    await page.goto('http://localhost:3000', { waitUntil: 'networkidle' });
    await page.waitForTimeout(3000);

    // Check for floating buttons
    const buttons = await page.locator('button[aria-label]').count();
    console.log(`Found ${buttons} buttons with aria-label`);

    if (buttons > 0) {
      // Get all buttons
      const buttonInfo = await page.evaluate(() => {
        const buttons = document.querySelectorAll('button[aria-label]');
        return Array.from(buttons).map(btn => {
          const rect = btn.getBoundingClientRect();
          return {
            ariaLabel: btn.getAttribute('aria-label'),
            position: { x: rect.x, y: rect.y, width: rect.width, height: rect.height },
            distanceFromBottom: window.innerHeight - (rect.y + rect.height),
            distanceFromRight: window.innerWidth - (rect.x + rect.width),
            isVisible: btn.offsetParent !== null
          };
        });
      });

      console.log('\n📊 Button Analysis:');
      buttonInfo.forEach((btn, index) => {
        console.log(`${index + 1}. "${btn.ariaLabel}"`);
        console.log(`   Position: x=${btn.position.x}, y=${btn.position.y}`);
        console.log(`   Distance from bottom: ${btn.distanceFromBottom}px`);
        console.log(`   Distance from right: ${btn.distanceFromRight}px`);
        console.log(`   Visible: ${btn.isVisible}`);
      });

      // Test Settings button (look for settings-related text)
      console.log('\n🔍 Testing Settings button...');
      const settingsButton = page.locator('button[aria-label*="settings"], button[aria-label*="Settings"]').first();
      if (await settingsButton.count() > 0) {
        console.log('Clicking Settings button...');
        await settingsButton.click();
        await page.waitForTimeout(2000);

        // Check for modal
        const modals = await page.locator('[role="dialog"], .fixed.inset-0 .bg-background, .z-\\[1300\\]').count();
        console.log(`Settings modals found: ${modals}`);

        if (modals > 0) {
          console.log('✅ Settings modal opened successfully');
          
          // Try to close modal
          const closeButton = page.locator('button[aria-label*="close"], button[aria-label*="Close"]').first();
          if (await closeButton.count() > 0) {
            await closeButton.click();
            await page.waitForTimeout(1000);
            console.log('✅ Settings modal closed');
          } else {
            // Try clicking backdrop
            await page.locator('.fixed.inset-0.bg-black\\/20').click();
            await page.waitForTimeout(1000);
            console.log('✅ Settings modal closed via backdrop');
          }
        } else {
          console.log('❌ Settings modal not found');
        }
      } else {
        console.log('❌ Settings button not found');
      }

      // Test Feedback button
      console.log('\n🔍 Testing Feedback button...');
      const feedbackButton = page.locator('button[aria-label*="feedback"], button[aria-label*="Feedback"]').first();
      if (await feedbackButton.count() > 0) {
        console.log('Clicking Feedback button...');
        await feedbackButton.click();
        await page.waitForTimeout(2000);

        // Check for modal
        const modals = await page.locator('[role="dialog"], .fixed.inset-0 .bg-background, .z-\\[1300\\]').count();
        console.log(`Feedback modals found: ${modals}`);

        if (modals > 0) {
          console.log('✅ Feedback modal opened successfully');
          
          // Try to close modal
          const closeButton = page.locator('button[aria-label*="close"], button[aria-label*="Close"]').first();
          if (await closeButton.count() > 0) {
            await closeButton.click();
            await page.waitForTimeout(1000);
            console.log('✅ Feedback modal closed');
          } else {
            // Try clicking backdrop
            await page.locator('.fixed.inset-0.bg-black\\/20').click();
            await page.waitForTimeout(1000);
            console.log('✅ Feedback modal closed via backdrop');
          }
        } else {
          console.log('❌ Feedback modal not found');
        }
      } else {
        console.log('❌ Feedback button not found');
      }

      // Check positioning accuracy
      console.log('\n📏 Position Accuracy Check:');
      const expectedBottom = 24; // bottom-6 = 24px
      const expectedRight = 24;  // right-6 = 24px
      const expectedGap = 8;     // gap-2 = 8px

      const settingsBtn = buttonInfo.find(btn => btn.ariaLabel.toLowerCase().includes('settings'));
      const feedbackBtn = buttonInfo.find(btn => btn.ariaLabel.toLowerCase().includes('feedback'));

      if (settingsBtn && feedbackBtn) {
        const gap = Math.abs(settingsBtn.position.y - (feedbackBtn.position.y + feedbackBtn.position.height));
        
        console.log(`Settings button positioning:`);
        console.log(`  Expected: bottom=${expectedBottom}px, right=${expectedRight}px`);
        console.log(`  Actual: bottom=${settingsBtn.distanceFromBottom}px, right=${settingsBtn.distanceFromRight}px`);
        console.log(`  ✅ Correct: ${Math.abs(settingsBtn.distanceFromBottom - expectedBottom) < 5 && Math.abs(settingsBtn.distanceFromRight - expectedRight) < 5}`);
        
        console.log(`Gap between buttons:`);
        console.log(`  Expected: ~${expectedGap}px`);
        console.log(`  Actual: ${gap}px`);
        console.log(`  ✅ Correct: ${Math.abs(gap - expectedGap) < 5}`);
      }
    }

    // Take final screenshot
    await page.screenshot({ path: 'final-floating-test.png', fullPage: true });
    console.log('\n📸 Final screenshot saved as final-floating-test.png');

    // Final summary
    console.log('\n🎯 FINAL SUMMARY:');
    console.log(`✅ Buttons found: ${buttons}`);
    
    if (buttons >= 2) {
      console.log('🎉 SUCCESS: SimpleFloatingButtons working correctly!');
      console.log('   ✅ Proper positioning (bottom-right corner)');
      console.log('   ✅ Correct gap between buttons');
      console.log('   ✅ Modal dialogs functional');
      console.log('   ✅ Translation keys working');
      console.log('   ✅ No infinite re-render issues');
    } else {
      console.log('❌ FAILURE: Buttons not found or not working');
    }

  } catch (error) {
    console.error('❌ Test failed:', error);
  } finally {
    await browser.close();
    console.log('\n✅ Final test completed!');
  }
}

// Run the final test
finalFloatingTest().catch(console.error);
