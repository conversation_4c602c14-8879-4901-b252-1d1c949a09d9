const { chromium } = require('playwright');

async function testFloatingButtons() {
  console.log('🚀 Starting Playwright test for floating buttons...');
  
  const browser = await chromium.launch({ headless: false });
  const context = await browser.newContext({
    viewport: { width: 1280, height: 720 }
  });
  const page = await context.newPage();

  try {
    // Navigate to localhost:3000
    console.log('📍 Navigating to localhost:3000...');
    await page.goto('http://localhost:3000', { waitUntil: 'networkidle' });
    
    // Wait for page to load
    await page.waitForTimeout(2000);
    
    // Take screenshot of initial state
    await page.screenshot({ path: 'floating-test-initial.png', fullPage: true });
    console.log('📸 Initial screenshot saved as floating-test-initial.png');

    // Test 1: Check for floating buttons
    console.log('\n🔍 Test 1: Looking for floating buttons...');
    
    // Look for settings button (gear icon)
    const settingsButton = await page.locator('[aria-label*="Settings"], [aria-label*="settings"], button:has(svg)').first();
    const feedbackButton = await page.locator('[aria-label*="Feedback"], [aria-label*="feedback"], button:has(svg)').nth(1);
    
    // Check if buttons exist
    const settingsExists = await settingsButton.count() > 0;
    const feedbackExists = await feedbackButton.count() > 0;
    
    console.log(`Settings button found: ${settingsExists}`);
    console.log(`Feedback button found: ${feedbackExists}`);

    if (settingsExists) {
      // Get settings button position
      const settingsBox = await settingsButton.boundingBox();
      if (settingsBox) {
        console.log(`Settings button position: x=${settingsBox.x}, y=${settingsBox.y}, width=${settingsBox.width}, height=${settingsBox.height}`);
        
        // Calculate distance from bottom-right
        const viewportHeight = 720;
        const viewportWidth = 1280;
        const distanceFromBottom = viewportHeight - (settingsBox.y + settingsBox.height);
        const distanceFromRight = viewportWidth - (settingsBox.x + settingsBox.width);
        
        console.log(`Settings button distance from bottom: ${distanceFromBottom}px`);
        console.log(`Settings button distance from right: ${distanceFromRight}px`);
      }
    }

    if (feedbackExists) {
      // Get feedback button position
      const feedbackBox = await feedbackButton.boundingBox();
      if (feedbackBox) {
        console.log(`Feedback button position: x=${feedbackBox.x}, y=${feedbackBox.y}, width=${feedbackBox.width}, height=${feedbackBox.height}`);
        
        // Calculate distance from bottom-right
        const viewportHeight = 720;
        const viewportWidth = 1280;
        const distanceFromBottom = viewportHeight - (feedbackBox.y + feedbackBox.height);
        const distanceFromRight = viewportWidth - (feedbackBox.x + feedbackBox.width);
        
        console.log(`Feedback button distance from bottom: ${distanceFromBottom}px`);
        console.log(`Feedback button distance from right: ${distanceFromRight}px`);
      }
    }

    // Calculate gap between buttons if both exist
    if (settingsExists && feedbackExists) {
      const settingsBox = await settingsButton.boundingBox();
      const feedbackBox = await feedbackButton.boundingBox();
      
      if (settingsBox && feedbackBox) {
        const gap = Math.abs(settingsBox.y - (feedbackBox.y + feedbackBox.height));
        console.log(`Gap between buttons: ${gap}px`);
        
        // Check if gap is reasonable (should be around 8px for new system)
        if (gap > 50) {
          console.log('⚠️  WARNING: Large gap detected! This might be the old system.');
        } else if (gap < 20) {
          console.log('✅ Good: Small gap detected. New system working correctly.');
        }
      }
    }

    // Test 2: Check specific test pages
    console.log('\n🔍 Test 2: Testing specific pages...');
    
    const testPages = [
      '/simple-test',
      '/step-by-step-test', 
      '/integrated-test',
      '/layout-test',
      '/comparison-test'
    ];

    for (const testPage of testPages) {
      try {
        console.log(`\n📄 Testing ${testPage}...`);
        await page.goto(`http://localhost:3000${testPage}`, { waitUntil: 'networkidle' });
        await page.waitForTimeout(1000);
        
        // Look for floating buttons on this page
        const buttons = await page.locator('button[aria-label], .fixed button').count();
        console.log(`Found ${buttons} buttons on ${testPage}`);
        
        // Take screenshot
        await page.screenshot({ path: `floating-test-${testPage.replace('/', '')}.png`, fullPage: true });
        console.log(`📸 Screenshot saved for ${testPage}`);
        
      } catch (error) {
        console.log(`❌ Error testing ${testPage}: ${error.message}`);
      }
    }

    // Test 3: Check DOM structure
    console.log('\n🔍 Test 3: Checking DOM structure...');
    await page.goto('http://localhost:3000', { waitUntil: 'networkidle' });
    
    // Look for floating elements with specific classes
    const floatingElements = await page.evaluate(() => {
      const elements = [];
      
      // Look for elements with fixed positioning
      const fixedElements = document.querySelectorAll('.fixed, [style*="position: fixed"]');
      fixedElements.forEach((el, index) => {
        const rect = el.getBoundingClientRect();
        const styles = window.getComputedStyle(el);
        
        elements.push({
          index,
          tagName: el.tagName,
          className: el.className,
          position: {
            x: rect.x,
            y: rect.y,
            width: rect.width,
            height: rect.height
          },
          styles: {
            position: styles.position,
            bottom: styles.bottom,
            right: styles.right,
            zIndex: styles.zIndex
          },
          ariaLabel: el.getAttribute('aria-label'),
          hasButton: el.tagName === 'BUTTON' || el.querySelector('button') !== null
        });
      });
      
      return elements;
    });

    console.log('\nFixed positioned elements found:');
    floatingElements.forEach((el, index) => {
      console.log(`${index + 1}. ${el.tagName} (${el.className})`);
      console.log(`   Position: x=${el.position.x}, y=${el.position.y}`);
      console.log(`   Size: ${el.position.width}x${el.position.height}`);
      console.log(`   CSS: bottom=${el.styles.bottom}, right=${el.styles.right}`);
      console.log(`   Z-index: ${el.styles.zIndex}`);
      console.log(`   Has button: ${el.hasButton}`);
      console.log(`   ARIA label: ${el.ariaLabel || 'none'}`);
      console.log('');
    });

    // Test 4: Interactive test
    console.log('\n🔍 Test 4: Interactive test...');
    
    if (settingsExists) {
      console.log('Clicking settings button...');
      await settingsButton.click();
      await page.waitForTimeout(1000);
      
      // Check if modal opened
      const modal = await page.locator('[role="dialog"], .modal, .fixed.inset-0').first();
      const modalExists = await modal.count() > 0;
      console.log(`Settings modal opened: ${modalExists}`);
      
      if (modalExists) {
        // Close modal
        const closeButton = await page.locator('button:has-text("×"), button[aria-label*="close"]').first();
        if (await closeButton.count() > 0) {
          await closeButton.click();
          await page.waitForTimeout(500);
          console.log('Settings modal closed');
        }
      }
    }

    // Final screenshot
    await page.screenshot({ path: 'floating-test-final.png', fullPage: true });
    console.log('📸 Final screenshot saved as floating-test-final.png');

  } catch (error) {
    console.error('❌ Test failed:', error);
  } finally {
    await browser.close();
    console.log('\n✅ Test completed!');
  }
}

// Run the test
testFloatingButtons().catch(console.error);
