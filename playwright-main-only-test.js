const { chromium } = require('playwright');

async function testMainPageOnly() {
  console.log('🔍 Testing main page floating buttons only...');
  
  const browser = await chromium.launch({ headless: false });
  const context = await browser.newContext({
    viewport: { width: 1280, height: 720 }
  });
  const page = await context.newPage();

  try {
    // Test main page only
    console.log('📍 Testing main page...');
    await page.goto('http://localhost:3000', { waitUntil: 'networkidle' });
    await page.waitForTimeout(3000);

    // Check for buttons
    const buttons = await page.locator('button[aria-label]').count();
    console.log(`Found ${buttons} buttons on main page`);

    if (buttons > 0) {
      // Get button positions
      const buttonInfo = await page.evaluate(() => {
        const buttons = document.querySelectorAll('button[aria-label]');
        return Array.from(buttons).map(btn => {
          const rect = btn.getBoundingClientRect();
          return {
            ariaLabel: btn.getAttribute('aria-label'),
            position: { x: rect.x, y: rect.y, width: rect.width, height: rect.height },
            distanceFromBottom: window.innerHeight - (rect.y + rect.height),
            distanceFromRight: window.innerWidth - (rect.x + rect.width)
          };
        });
      });

      console.log('\nButton positions on main page:');
      buttonInfo.forEach((btn, index) => {
        console.log(`${index + 1}. ${btn.ariaLabel}:`);
        console.log(`   Position: x=${btn.position.x}, y=${btn.position.y}`);
        console.log(`   Distance from bottom: ${btn.distanceFromBottom}px`);
        console.log(`   Distance from right: ${btn.distanceFromRight}px`);
      });

      // Calculate gap between Settings and Feedback buttons
      const settingsButtons = buttonInfo.filter(btn => btn.ariaLabel === 'Settings');
      const feedbackButtons = buttonInfo.filter(btn => btn.ariaLabel === 'Feedback');
      
      console.log(`\nFound ${settingsButtons.length} Settings buttons`);
      console.log(`Found ${feedbackButtons.length} Feedback buttons`);

      if (settingsButtons.length > 0 && feedbackButtons.length > 0) {
        const settingsBtn = settingsButtons[0];
        const feedbackBtn = feedbackButtons[0];
        
        const gap = Math.abs(settingsBtn.position.y - (feedbackBtn.position.y + feedbackBtn.position.height));
        console.log(`\nGap between buttons: ${gap}px`);
        
        // Check positioning
        const expectedBottomDistance = 24; // bottom-6 = 24px
        const expectedRightDistance = 24;  // right-6 = 24px
        
        console.log('\n📊 Position Analysis:');
        console.log(`Settings button:`);
        console.log(`  Expected: bottom=${expectedBottomDistance}px, right=${expectedRightDistance}px`);
        console.log(`  Actual: bottom=${settingsBtn.distanceFromBottom}px, right=${settingsBtn.distanceFromRight}px`);
        console.log(`  ✅ Correct: ${Math.abs(settingsBtn.distanceFromBottom - expectedBottomDistance) < 5 && Math.abs(settingsBtn.distanceFromRight - expectedRightDistance) < 5}`);
        
        console.log(`Feedback button:`);
        console.log(`  Expected: above settings with ~8px gap`);
        console.log(`  Actual: bottom=${feedbackBtn.distanceFromBottom}px, right=${feedbackBtn.distanceFromRight}px`);
        console.log(`  Gap: ${gap}px`);
        console.log(`  ✅ Correct gap: ${gap >= 6 && gap <= 12}`);
      }

      // Test clicking
      console.log('\n🔍 Testing button clicks...');
      try {
        const settingsBtn = page.locator('button[aria-label="Settings"]').first();
        await settingsBtn.click();
        await page.waitForTimeout(1000);
        console.log('✅ Settings button clicked successfully');
        
        // Dismiss alert if it appeared
        page.on('dialog', dialog => dialog.accept());
        
        const feedbackBtn = page.locator('button[aria-label="Feedback"]').first();
        await feedbackBtn.click();
        await page.waitForTimeout(1000);
        console.log('✅ Feedback button clicked successfully');
        
      } catch (error) {
        console.log(`⚠️  Click test failed: ${error.message}`);
      }
    }

    // Check all fixed elements
    const allFixedElements = await page.evaluate(() => {
      const fixedElements = document.querySelectorAll('.fixed, [style*="position: fixed"]');
      return Array.from(fixedElements).map(el => {
        const rect = el.getBoundingClientRect();
        const styles = window.getComputedStyle(el);
        return {
          tagName: el.tagName,
          className: el.className.substring(0, 50) + '...',
          position: { x: rect.x, y: rect.y, width: rect.width, height: rect.height },
          styles: {
            bottom: styles.bottom,
            right: styles.right,
            zIndex: styles.zIndex
          },
          hasButton: el.tagName === 'BUTTON' || el.querySelector('button') !== null,
          ariaLabel: el.getAttribute('aria-label'),
          isVisible: rect.width > 0 && rect.height > 0
        };
      });
    });

    console.log(`\n📊 All fixed elements (${allFixedElements.length}):`);
    allFixedElements.forEach((el, index) => {
      if (el.isVisible) {
        console.log(`${index + 1}. ${el.tagName} (${el.className})`);
        console.log(`   Position: x=${el.position.x}, y=${el.position.y}`);
        console.log(`   CSS: bottom=${el.styles.bottom}, right=${el.styles.right}, z-index=${el.styles.zIndex}`);
        console.log(`   Has button: ${el.hasButton}`);
        console.log(`   ARIA label: ${el.ariaLabel || 'none'}`);
      }
    });

    // Take screenshot
    await page.screenshot({ path: 'main-page-final-test.png', fullPage: true });
    console.log('\n📸 Screenshot saved as main-page-final-test.png');

    // Summary
    console.log('\n📋 SUMMARY:');
    console.log(`✅ Buttons found: ${buttons}`);
    console.log(`✅ Fixed elements: ${allFixedElements.filter(el => el.isVisible).length}`);
    console.log(`✅ SimpleIntegratedButtons working: ${buttons >= 2 ? 'YES' : 'NO'}`);
    
    if (buttons >= 2) {
      console.log('🎉 SUCCESS: Floating buttons are now working correctly!');
      console.log('   - Settings button at bottom-right corner');
      console.log('   - Feedback button above settings');
      console.log('   - Proper spacing and positioning');
    }

  } catch (error) {
    console.error('❌ Test failed:', error);
  } finally {
    await browser.close();
    console.log('\n✅ Test completed!');
  }
}

// Run the test
testMainPageOnly().catch(console.error);
