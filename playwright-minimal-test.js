const { chromium } = require('playwright');

async function testMinimalFloating() {
  console.log('🔍 Testing minimal floating buttons...');
  
  const browser = await chromium.launch({ headless: false });
  const context = await browser.newContext({
    viewport: { width: 1280, height: 720 }
  });
  const page = await context.newPage();

  try {
    // Test minimal page first
    console.log('📍 Testing minimal page...');
    await page.goto('http://localhost:3000/minimal-test', { waitUntil: 'networkidle' });
    await page.waitForTimeout(2000);

    // Check for buttons
    const buttons = await page.locator('button[aria-label]').count();
    console.log(`Found ${buttons} buttons on minimal test page`);

    if (buttons > 0) {
      // Get button positions
      const buttonInfo = await page.evaluate(() => {
        const buttons = document.querySelectorAll('button[aria-label]');
        return Array.from(buttons).map(btn => {
          const rect = btn.getBoundingClientRect();
          return {
            ariaLabel: btn.getAttribute('aria-label'),
            position: { x: rect.x, y: rect.y, width: rect.width, height: rect.height },
            distanceFromBottom: window.innerHeight - (rect.y + rect.height),
            distanceFromRight: window.innerWidth - (rect.x + rect.width)
          };
        });
      });

      console.log('\nButton positions:');
      buttonInfo.forEach((btn, index) => {
        console.log(`${index + 1}. ${btn.ariaLabel}:`);
        console.log(`   Position: x=${btn.position.x}, y=${btn.position.y}`);
        console.log(`   Distance from bottom: ${btn.distanceFromBottom}px`);
        console.log(`   Distance from right: ${btn.distanceFromRight}px`);
      });

      // Calculate gap between buttons
      if (buttonInfo.length >= 2) {
        const settingsBtn = buttonInfo.find(btn => btn.ariaLabel === 'Settings');
        const feedbackBtn = buttonInfo.find(btn => btn.ariaLabel === 'Feedback');
        
        if (settingsBtn && feedbackBtn) {
          const gap = Math.abs(settingsBtn.position.y - (feedbackBtn.position.y + feedbackBtn.position.height));
          console.log(`\nGap between buttons: ${gap}px`);
          
          if (gap <= 10) {
            console.log('✅ Good: Small gap detected');
          } else {
            console.log('⚠️  Large gap detected');
          }
        }
      }

      // Test clicking
      console.log('\n🔍 Testing button clicks...');
      const settingsBtn = page.locator('button[aria-label="Settings"]');
      if (await settingsBtn.count() > 0) {
        await settingsBtn.click();
        await page.waitForTimeout(1000);
        console.log('✅ Settings button clicked');
      }
    }

    // Take screenshot
    await page.screenshot({ path: 'minimal-test.png', fullPage: true });
    console.log('📸 Screenshot saved as minimal-test.png');

    // Now test main page
    console.log('\n📍 Testing main page...');
    await page.goto('http://localhost:3000', { waitUntil: 'networkidle' });
    await page.waitForTimeout(3000);

    // Check for any floating elements
    const mainPageElements = await page.evaluate(() => {
      const fixedElements = document.querySelectorAll('.fixed, [style*="position: fixed"]');
      return Array.from(fixedElements).map(el => {
        const rect = el.getBoundingClientRect();
        const styles = window.getComputedStyle(el);
        return {
          tagName: el.tagName,
          className: el.className,
          position: { x: rect.x, y: rect.y, width: rect.width, height: rect.height },
          styles: {
            bottom: styles.bottom,
            right: styles.right,
            zIndex: styles.zIndex
          },
          hasButton: el.tagName === 'BUTTON' || el.querySelector('button') !== null,
          ariaLabel: el.getAttribute('aria-label')
        };
      });
    });

    console.log(`\nFound ${mainPageElements.length} fixed elements on main page:`);
    mainPageElements.forEach((el, index) => {
      console.log(`${index + 1}. ${el.tagName} (${el.className})`);
      console.log(`   Position: x=${el.position.x}, y=${el.position.y}`);
      console.log(`   CSS: bottom=${el.styles.bottom}, right=${el.styles.right}`);
      console.log(`   Has button: ${el.hasButton}`);
      console.log(`   ARIA label: ${el.ariaLabel || 'none'}`);
    });

    // Take screenshot of main page
    await page.screenshot({ path: 'main-page-test.png', fullPage: true });
    console.log('📸 Main page screenshot saved as main-page-test.png');

  } catch (error) {
    console.error('❌ Test failed:', error);
  } finally {
    await browser.close();
    console.log('\n✅ Test completed!');
  }
}

// Run the test
testMinimalFloating().catch(console.error);
