'use client';

import React from 'react';
import { Button } from '@/components/ui';
import { Settings, MessageSquare } from 'lucide-react';

export default function ComparisonTestPage() {
	return (
		<div className="min-h-screen p-8 space-y-8">
			<div className="space-y-4">
				<h1 className="text-3xl font-bold">Comparison Test</h1>
				<p className="text-muted-foreground">
					So sánh hệ thống cũ (fixed positions) vs hệ thống mới (floating system)
				</p>
			</div>

			{/* Current Issue Visualization */}
			<div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
				{/* Old System */}
				<div className="space-y-4">
					<h2 className="text-xl font-semibold text-red-600">Old System (Current Issue)</h2>
					<div className="relative h-96 border rounded-lg bg-muted/10">
						<div className="absolute bottom-4 right-4 w-12 h-12 bg-blue-500 rounded-full flex items-center justify-center text-white text-xs">
							<Settings className="h-4 w-4" />
							<div className="absolute -bottom-8 left-1/2 transform -translate-x-1/2 text-xs text-muted-foreground whitespace-nowrap">
								Settings (16px)
							</div>
						</div>
						<div className="absolute bottom-36 right-4 w-12 h-12 bg-green-500 rounded-full flex items-center justify-center text-white text-xs">
							<MessageSquare className="h-4 w-4" />
							<div className="absolute -bottom-8 left-1/2 transform -translate-x-1/2 text-xs text-muted-foreground whitespace-nowrap">
								Feedback (144px)
							</div>
						</div>
						<div className="absolute bottom-20 right-6 text-red-500 text-xs font-bold">
							128px GAP!
						</div>
						<div className="absolute bottom-20 right-8 w-0.5 h-16 bg-red-500"></div>
					</div>
					<div className="text-sm space-y-1">
						<div className="text-red-600">❌ Settings: bottom: 16px (useDOMFloating)</div>
						<div className="text-red-600">❌ Feedback: bottom: 144px (useFloatingUIElement)</div>
						<div className="text-red-600">❌ Gap: 128px (too large)</div>
						<div className="text-red-600">❌ Different systems: DOM + UI</div>
					</div>
				</div>

				{/* New System */}
				<div className="space-y-4">
					<h2 className="text-xl font-semibold text-green-600">New System (Fixed)</h2>
					<div className="relative h-96 border rounded-lg bg-muted/10">
						<div className="absolute bottom-4 right-4 w-12 h-12 bg-blue-500 rounded-full flex items-center justify-center text-white text-xs">
							<Settings className="h-4 w-4" />
							<div className="absolute -bottom-8 left-1/2 transform -translate-x-1/2 text-xs text-muted-foreground whitespace-nowrap">
								Settings (P:10)
							</div>
						</div>
						<div className="absolute bottom-20 right-4 w-12 h-12 bg-green-500 rounded-full flex items-center justify-center text-white text-xs">
							<MessageSquare className="h-4 w-4" />
							<div className="absolute -bottom-8 left-1/2 transform -translate-x-1/2 text-xs text-muted-foreground whitespace-nowrap">
								Feedback (P:8)
							</div>
						</div>
						<div className="absolute bottom-12 right-6 text-green-500 text-xs font-bold">
							8px gap
						</div>
						<div className="absolute bottom-12 right-8 w-0.5 h-4 bg-green-500"></div>
					</div>
					<div className="text-sm space-y-1">
						<div className="text-green-600">✅ Settings: Priority 10 (bottom-most)</div>
						<div className="text-green-600">✅ Feedback: Priority 8 (above settings)</div>
						<div className="text-green-600">✅ Gap: 8px (configurable)</div>
						<div className="text-green-600">✅ Unified system: FloatingButtonManager</div>
					</div>
				</div>
			</div>

			{/* Test Current System */}
			<div className="space-y-4">
				<h2 className="text-xl font-semibold">Test Current System</h2>
				<p className="text-sm text-muted-foreground">
					Kiểm tra hệ thống hiện tại đang chạy trên localhost:3000
				</p>
				<div className="flex flex-wrap gap-2">
					<Button 
						onClick={() => window.open('http://localhost:3000', '_blank')}
					>
						Open Main Page
					</Button>
					<Button 
						variant="outline"
						onClick={() => window.open('http://localhost:3000/simple-test', '_blank')}
					>
						Simple Test
					</Button>
					<Button 
						variant="outline"
						onClick={() => window.open('http://localhost:3000/step-by-step-test', '_blank')}
					>
						Step by Step Test
					</Button>
					<Button 
						variant="outline"
						onClick={() => window.open('http://localhost:3000/integrated-test', '_blank')}
					>
						Integrated Test
					</Button>
				</div>
			</div>

			{/* Manual Test Buttons */}
			<div className="space-y-4">
				<h2 className="text-xl font-semibold">Manual Test Buttons</h2>
				<p className="text-sm text-muted-foreground">
					Test buttons với spacing đúng (8px gap)
				</p>
			</div>

			{/* Fixed Test Buttons - New System Style */}
			<div className="fixed bottom-6 right-6 z-50 flex flex-col-reverse items-end gap-2">
				<Button
					size="icon"
					className="h-12 w-12 rounded-full shadow-md bg-background border hover:shadow-lg transition-all duration-200"
					onClick={() => alert('New System Settings clicked!')}
					aria-label="Settings"
				>
					<Settings className="h-4 w-4" />
				</Button>
				<Button
					size="icon"
					className="h-12 w-12 rounded-full shadow-md bg-background border hover:shadow-lg transition-all duration-200"
					onClick={() => alert('New System Feedback clicked!')}
					aria-label="Feedback"
				>
					<MessageSquare className="h-4 w-4" />
				</Button>
			</div>

			{/* Instructions */}
			<div className="space-y-4">
				<h2 className="text-xl font-semibold">Test Instructions</h2>
				<div className="grid grid-cols-1 md:grid-cols-2 gap-6">
					<div className="space-y-2">
						<h3 className="font-medium">Visual Check:</h3>
						<ul className="text-sm space-y-1 list-disc list-inside">
							<li>Check bottom-right corner</li>
							<li>Settings button should be at bottom</li>
							<li>Feedback button should be above settings</li>
							<li>Gap should be small (~8px)</li>
							<li>No large empty space between buttons</li>
						</ul>
					</div>
					<div className="space-y-2">
						<h3 className="font-medium">Functional Check:</h3>
						<ul className="text-sm space-y-1 list-disc list-inside">
							<li>Click settings button → should work</li>
							<li>Click feedback button → should work</li>
							<li>Hover effects should work</li>
							<li>Buttons should be responsive</li>
							<li>No console errors</li>
						</ul>
					</div>
				</div>
			</div>

			{/* Expected vs Actual */}
			<div className="space-y-4">
				<h2 className="text-xl font-semibold">Expected vs Actual</h2>
				<div className="grid grid-cols-1 md:grid-cols-2 gap-4">
					<div className="p-4 border rounded-lg bg-green-50 dark:bg-green-950/20">
						<h3 className="font-medium text-green-700 dark:text-green-300 mb-2">Expected (Fixed)</h3>
						<ul className="text-sm space-y-1">
							<li>Settings: bottom-right corner</li>
							<li>Feedback: 8px above settings</li>
							<li>Total gap: 8px</li>
							<li>Consistent spacing</li>
							<li>Priority-based ordering</li>
						</ul>
					</div>
					<div className="p-4 border rounded-lg bg-red-50 dark:bg-red-950/20">
						<h3 className="font-medium text-red-700 dark:text-red-300 mb-2">Actual (Before Fix)</h3>
						<ul className="text-sm space-y-1">
							<li>Settings: bottom: 16px</li>
							<li>Feedback: bottom: 144px</li>
							<li>Total gap: 128px</li>
							<li>Large empty space</li>
							<li>Hard-coded positions</li>
						</ul>
					</div>
				</div>
			</div>
		</div>
	);
}
