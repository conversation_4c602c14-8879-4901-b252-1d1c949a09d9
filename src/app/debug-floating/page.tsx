'use client';

import React, { useEffect } from 'react';
import { Button } from '@/components/ui';
import { useFloatingButtonSystem } from '@/components/floating-ui';
import { Settings, MessageSquare } from 'lucide-react';

export default function DebugFloatingPage() {
	const system = useFloatingButtonSystem('debug-system', {
		position: 'bottom-right',
		gap: 2,
		maxVisible: 5,
		collisionDetection: true,
	});

	useEffect(() => {
		console.log('Registering debug buttons...');
		
		system.registerButton({
			id: 'debug-settings',
			label: 'Debug Settings',
			icon: <Settings className="h-4 w-4" />,
			onClick: () => {
				console.log('Debug Settings clicked!');
				alert('Debug Settings clicked!');
			},
			priority: 10,
		});

		system.registerButton({
			id: 'debug-feedback',
			label: 'Debug Feedback',
			icon: <MessageSquare className="h-4 w-4" />,
			onClick: () => {
				console.log('Debug Feedback clicked!');
				alert('Debug Feedback clicked!');
			},
			priority: 8,
		});

		console.log('Buttons registered:', system.buttons);

		return () => {
			console.log('Unregistering debug buttons...');
			system.unregisterButton('debug-settings');
			system.unregisterButton('debug-feedback');
		};
	}, [system]);

	// Debug info
	useEffect(() => {
		console.log('System state:', system.systemState);
		console.log('Buttons:', system.buttons);
		console.log('Button positions:', system.calculateButtonPositions());
	}, [system.buttons, system.systemState, system.calculateButtonPositions]);

	return (
		<div className="min-h-screen p-8 space-y-8">
			<div className="space-y-4">
				<h1 className="text-3xl font-bold">Debug Floating System</h1>
				<p className="text-muted-foreground">
					Debug page để kiểm tra floating button system
				</p>
			</div>

			{/* Debug Info */}
			<div className="space-y-4">
				<h2 className="text-xl font-semibold">Debug Info</h2>
				<div className="grid grid-cols-1 md:grid-cols-2 gap-4">
					<div className="p-4 border rounded-lg bg-muted/20">
						<h3 className="font-medium mb-2">System State</h3>
						<pre className="text-xs overflow-auto">
							{JSON.stringify(system.systemState, null, 2)}
						</pre>
					</div>
					<div className="p-4 border rounded-lg bg-muted/20">
						<h3 className="font-medium mb-2">Buttons ({system.buttons.length})</h3>
						<div className="space-y-2">
							{system.buttons.map((button) => (
								<div key={button.id} className="text-xs p-2 bg-background rounded">
									<div>ID: {button.id}</div>
									<div>Priority: {button.priority}</div>
									<div>Visible: {button.visible ? 'Yes' : 'No'}</div>
								</div>
							))}
						</div>
					</div>
				</div>
			</div>

			{/* Manual Controls */}
			<div className="space-y-4">
				<h2 className="text-xl font-semibold">Manual Controls</h2>
				<div className="flex flex-wrap gap-2">
					<Button onClick={() => system.showAllButtons()}>
						Show All Buttons
					</Button>
					<Button onClick={() => system.hideAllButtons()}>
						Hide All Buttons
					</Button>
					<Button onClick={() => system.showButton('debug-settings')}>
						Show Settings
					</Button>
					<Button onClick={() => system.hideButton('debug-settings')}>
						Hide Settings
					</Button>
					<Button onClick={() => system.showButton('debug-feedback')}>
						Show Feedback
					</Button>
					<Button onClick={() => system.hideButton('debug-feedback')}>
						Hide Feedback
					</Button>
				</div>
			</div>

			{/* Manual Floating Buttons for comparison */}
			<div className="space-y-4">
				<h2 className="text-xl font-semibold">Manual Buttons (for comparison)</h2>
				<div className="fixed bottom-6 left-6 z-50 flex flex-col-reverse items-start gap-2">
					<Button
						size="icon"
						className="h-12 w-12 rounded-full shadow-md bg-background border"
						onClick={() => alert('Manual Settings clicked!')}
					>
						<Settings className="h-4 w-4" />
					</Button>
					<Button
						size="icon"
						className="h-12 w-12 rounded-full shadow-md bg-background border"
						onClick={() => alert('Manual Feedback clicked!')}
					>
						<MessageSquare className="h-4 w-4" />
					</Button>
				</div>
			</div>

			{/* System Buttons - Rendered through FloatingButtonRenderer */}
			<div className="space-y-4">
				<h2 className="text-xl font-semibold">System Buttons</h2>
				<p className="text-sm text-muted-foreground">
					These should appear at bottom-right corner through the floating system
				</p>
				
				{/* Direct render for debugging */}
				<div className="fixed bottom-6 right-6 z-50 flex flex-col-reverse items-end gap-2">
					{system.buttons.map((button) => (
						<Button
							key={button.id}
							size="icon"
							className="h-12 w-12 rounded-full shadow-md bg-background border hover:shadow-lg transition-all duration-200"
							onClick={button.onClick}
							aria-label={button.label}
						>
							{button.icon}
						</Button>
					))}
				</div>
			</div>

			{/* Instructions */}
			<div className="space-y-4">
				<h2 className="text-xl font-semibold">Expected Results</h2>
				<ul className="space-y-2 text-sm">
					<li>• Bottom-left: Manual buttons (for comparison)</li>
					<li>• Bottom-right: System buttons (Settings at bottom, Feedback above)</li>
					<li>• Console logs should show button registration and state changes</li>
					<li>• Clicking buttons should show alerts</li>
					<li>• Show/Hide controls should work</li>
				</ul>
			</div>
		</div>
	);
}
