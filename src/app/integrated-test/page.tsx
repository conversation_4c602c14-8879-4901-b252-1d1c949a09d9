'use client';

import React from 'react';
import { IntegratedFloatingButtons } from '@/components/floating-ui';

export default function IntegratedTestPage() {
	return (
		<div className="min-h-screen p-8">
			<div className="space-y-4">
				<h1 className="text-3xl font-bold">Integrated Floating Buttons Test</h1>
				<p className="text-muted-foreground">
					Test IntegratedFloatingButtons component với settings và feedback
				</p>
			</div>

			<div className="space-y-6">
				<div className="space-y-2">
					<h2 className="text-xl font-semibold">Expected Results:</h2>
					<ul className="space-y-1 text-sm list-disc list-inside">
						<li>Settings button ở bottom-right corner (priority 10 - gần bottom nhất)</li>
						<li>Feedback button ở phía trên settings button (priority 8)</li>
						<li>Gap giữa 2 button là 8px (gap=2)</li>
						<li>Click settings button → mở settings modal</li>
						<li>Click feedback button → mở feedback modal</li>
						<li>Modal có overlay và có thể đóng bằng X hoặc click outside</li>
					</ul>
				</div>

				<div className="space-y-2">
					<h2 className="text-xl font-semibold">Test Actions:</h2>
					<ol className="space-y-1 text-sm list-decimal list-inside">
						<li>Kiểm tra vị trí buttons ở bottom-right</li>
						<li>Click settings button (gear icon)</li>
						<li>Test settings modal (language, theme, logout)</li>
						<li>Đóng settings modal</li>
						<li>Click feedback button (message icon)</li>
						<li>Test feedback modal (nhập message, submit)</li>
						<li>Đóng feedback modal</li>
						<li>Kiểm tra responsive behavior</li>
					</ol>
				</div>

				<div className="p-4 border rounded-lg bg-muted/20">
					<h3 className="font-medium mb-2">Debug Info:</h3>
					<p className="text-sm">
						Nếu buttons không hiển thị, check console để xem có lỗi gì.
						Buttons sẽ được render qua FloatingButtonManager với systemId "integrated-test".
					</p>
				</div>
			</div>

			{/* Integrated Floating Buttons */}
			<IntegratedFloatingButtons
				systemId="integrated-test"
				position="bottom-right"
				gap={2}
				includeSettings={true}
				includeFeedback={true}
			/>
		</div>
	);
}
