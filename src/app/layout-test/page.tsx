'use client';

import React from 'react';
import { IntegratedFloatingButtons } from '@/components/floating-ui';

export default function LayoutTestPage() {
	return (
		<div className="min-h-screen p-8 space-y-8">
			<div className="space-y-4">
				<h1 className="text-3xl font-bold">Layout Test</h1>
				<p className="text-muted-foreground">
					Test IntegratedFloatingButtons component thay thế hệ thống cũ
				</p>
			</div>

			<div className="space-y-6">
				<div className="p-4 border rounded-lg bg-blue-50 dark:bg-blue-950/20">
					<h2 className="text-lg font-semibold mb-2">Migration Plan</h2>
					<div className="text-sm space-y-2">
						<p><strong>Current Layout (app/layout.tsx):</strong></p>
						<code className="block bg-muted p-2 rounded text-xs">
							{`<DOMClientSettings />
<FloatingFeedback />`}
						</code>
						<p><strong>New Layout (proposed):</strong></p>
						<code className="block bg-muted p-2 rounded text-xs">
							{`<IntegratedFloatingButtons />`}
						</code>
					</div>
				</div>

				<div className="space-y-4">
					<h2 className="text-xl font-semibold">Test Results</h2>
					<div className="grid grid-cols-1 md:grid-cols-2 gap-4">
						<div className="p-4 border rounded-lg">
							<h3 className="font-medium mb-2">Expected Behavior:</h3>
							<ul className="text-sm space-y-1 list-disc list-inside">
								<li>Settings button at bottom-right (priority 10)</li>
								<li>Feedback button above settings (priority 8)</li>
								<li>8px gap between buttons</li>
								<li>Settings modal opens on click</li>
								<li>Feedback modal opens on click</li>
								<li>Modals can be closed</li>
							</ul>
						</div>
						<div className="p-4 border rounded-lg">
							<h3 className="font-medium mb-2">Check List:</h3>
							<ul className="text-sm space-y-1">
								<li>□ Buttons visible at bottom-right</li>
								<li>□ Correct ordering (Settings bottom, Feedback top)</li>
								<li>□ Small gap between buttons</li>
								<li>□ Settings modal works</li>
								<li>□ Feedback modal works</li>
								<li>□ No console errors</li>
							</ul>
						</div>
					</div>
				</div>

				<div className="space-y-4">
					<h2 className="text-xl font-semibold">Implementation Notes</h2>
					<div className="text-sm space-y-2">
						<div className="p-3 bg-yellow-50 dark:bg-yellow-950/20 rounded">
							<strong>Note:</strong> This page uses IntegratedFloatingButtons component 
							which should replace both DOMClientSettings and FloatingFeedback in the main layout.
						</div>
						<div className="p-3 bg-green-50 dark:bg-green-950/20 rounded">
							<strong>Benefits:</strong> Unified system, consistent spacing, priority-based ordering, 
							easier maintenance, better collision detection.
						</div>
						<div className="p-3 bg-blue-50 dark:bg-blue-950/20 rounded">
							<strong>Configuration:</strong> Position, gap, and inclusion of buttons can be configured 
							through props.
						</div>
					</div>
				</div>

				<div className="space-y-4">
					<h2 className="text-xl font-semibold">Debug Information</h2>
					<div className="p-4 border rounded-lg bg-muted/20">
						<div className="text-sm space-y-1">
							<div>Component: IntegratedFloatingButtons</div>
							<div>System ID: layout-test</div>
							<div>Position: bottom-right</div>
							<div>Gap: 2 (8px)</div>
							<div>Include Settings: true</div>
							<div>Include Feedback: true</div>
						</div>
					</div>
				</div>

				<div className="space-y-4">
					<h2 className="text-xl font-semibold">Test Actions</h2>
					<div className="text-sm space-y-2">
						<ol className="list-decimal list-inside space-y-1">
							<li>Look at bottom-right corner of screen</li>
							<li>Verify settings button (gear icon) is at the bottom</li>
							<li>Verify feedback button (message icon) is above settings</li>
							<li>Click settings button to open modal</li>
							<li>Test settings modal functionality (language, theme, logout)</li>
							<li>Close settings modal</li>
							<li>Click feedback button to open modal</li>
							<li>Test feedback modal (enter message, submit)</li>
							<li>Close feedback modal</li>
							<li>Check browser console for any errors</li>
						</ol>
					</div>
				</div>
			</div>

			{/* Integrated Floating Buttons */}
			<IntegratedFloatingButtons
				systemId="layout-test"
				position="bottom-right"
				gap={2}
				includeSettings={true}
				includeFeedback={true}
			/>
		</div>
	);
}
