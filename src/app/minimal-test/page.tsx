'use client';

import React from 'react';
import { Button } from '@/components/ui';
import { Settings, MessageSquare } from 'lucide-react';

export default function MinimalTestPage() {
	return (
		<div className="min-h-screen p-8">
			<div className="space-y-4">
				<h1 className="text-3xl font-bold">Minimal Test</h1>
				<p className="text-muted-foreground">
					Test minimal floating buttons without complex system
				</p>
			</div>

			{/* Simple fixed buttons */}
			<div className="fixed bottom-6 right-6 z-50 flex flex-col-reverse items-end gap-2">
				<Button
					size="icon"
					className="h-12 w-12 rounded-full shadow-md bg-background border hover:shadow-lg transition-all duration-200"
					onClick={() => alert('Settings clicked!')}
					aria-label="Settings"
				>
					<Settings className="h-4 w-4" />
				</Button>
				<Button
					size="icon"
					className="h-12 w-12 rounded-full shadow-md bg-background border hover:shadow-lg transition-all duration-200"
					onClick={() => alert('Feedback clicked!')}
					aria-label="Feedback"
				>
					<MessageSquare className="h-4 w-4" />
				</Button>
			</div>

			<div className="space-y-4">
				<h2 className="text-xl font-semibold">Expected Results</h2>
				<ul className="space-y-1 text-sm list-disc list-inside">
					<li>Settings button at bottom-right corner (bottom button)</li>
					<li>Feedback button above settings button</li>
					<li>8px gap between buttons</li>
					<li>Both buttons clickable with alerts</li>
				</ul>
			</div>
		</div>
	);
}
