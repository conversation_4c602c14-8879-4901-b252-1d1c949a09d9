'use client';

import React from 'react';
import { Button } from '@/components/ui';
import { Settings, MessageSquare, Plus } from 'lucide-react';

export default function SimpleTestPage() {
	return (
		<div className="min-h-screen p-8">
			<div className="space-y-4">
				<h1 className="text-3xl font-bold">Simple Floating Test</h1>
				<p className="text-muted-foreground">
					Test cơ bản để kiểm tra floating buttons
				</p>
			</div>

			{/* Test 1: Basic Fixed Buttons */}
			<div className="fixed bottom-6 right-6 z-50 flex flex-col-reverse items-end gap-2">
				<Button
					size="icon"
					className="h-12 w-12 rounded-full shadow-md bg-background border hover:shadow-lg transition-all duration-200"
					onClick={() => alert('Settings clicked!')}
					aria-label="Settings"
				>
					<Settings className="h-4 w-4" />
				</Button>
				<Button
					size="icon"
					className="h-12 w-12 rounded-full shadow-md bg-background border hover:shadow-lg transition-all duration-200"
					onClick={() => alert('Feedback clicked!')}
					aria-label="Feedback"
				>
					<MessageSquare className="h-4 w-4" />
				</Button>
			</div>

			{/* Test 2: Different Position */}
			<div className="fixed bottom-6 left-6 z-50 flex flex-col-reverse items-start gap-2">
				<Button
					size="icon"
					className="h-14 w-14 rounded-full shadow-lg bg-primary text-primary-foreground hover:shadow-xl transition-all duration-200"
					onClick={() => alert('Plus clicked!')}
					aria-label="Add"
				>
					<Plus className="h-6 w-6" />
				</Button>
			</div>

			{/* Test 3: Top Position */}
			<div className="fixed top-6 right-6 z-50 flex flex-col items-end gap-2">
				<Button
					size="icon"
					variant="outline"
					className="h-10 w-10 rounded-full shadow-sm bg-background hover:shadow-md transition-all duration-200"
					onClick={() => alert('Top button clicked!')}
					aria-label="Top"
				>
					<Settings className="h-3 w-3" />
				</Button>
			</div>

			{/* Instructions */}
			<div className="max-w-2xl space-y-4">
				<h2 className="text-xl font-semibold">Test Instructions</h2>
				<div className="space-y-2 text-sm">
					<p><strong>Bottom-right:</strong> Settings button (bottom) và Feedback button (top)</p>
					<p><strong>Bottom-left:</strong> Plus button (main action)</p>
					<p><strong>Top-right:</strong> Small settings button</p>
				</div>
				
				<h3 className="text-lg font-medium">Expected Behavior:</h3>
				<ul className="space-y-1 text-sm list-disc list-inside">
					<li>All buttons should be visible and clickable</li>
					<li>Hover effects should work (shadow and scale)</li>
					<li>Buttons should have proper spacing (8px gap)</li>
					<li>Settings button should be closest to bottom-right corner</li>
					<li>Feedback button should be above settings button</li>
					<li>No overlapping or collision issues</li>
				</ul>

				<h3 className="text-lg font-medium">Visual Check:</h3>
				<ul className="space-y-1 text-sm list-disc list-inside">
					<li>Settings icon in bottom button (bottom-right)</li>
					<li>Message icon in top button (bottom-right)</li>
					<li>Plus icon in left button</li>
					<li>Small settings icon in top-right</li>
				</ul>
			</div>
		</div>
	);
}
