'use client';

import React, { useEffect, useState } from 'react';
import { Button } from '@/components/ui';
import { 
	useFloatingButtonSystem,
	FloatingButtonManager 
} from '@/components/floating-ui';
import { Settings, MessageSquare } from 'lucide-react';

export default function StepByStepTestPage() {
	const [step, setStep] = useState(1);
	const [logs, setLogs] = useState<string[]>([]);

	const addLog = (message: string) => {
		setLogs(prev => [...prev, `${new Date().toLocaleTimeString()}: ${message}`]);
	};

	// Step 1: Test basic hook
	const system = useFloatingButtonSystem('step-test', {
		position: 'bottom-right',
		gap: 2,
		maxVisible: 5,
	});

	useEffect(() => {
		addLog(`System initialized with ${system.buttons.length} buttons`);
	}, [system.buttons.length]);

	// Step 2: Register buttons
	const registerButtons = () => {
		addLog('Registering settings button...');
		system.registerButton({
			id: 'step-settings',
			label: 'Step Settings',
			icon: <Settings className="h-4 w-4" />,
			onClick: () => {
				addLog('Settings button clicked!');
				alert('Settings clicked!');
			},
			priority: 10,
		});

		addLog('Registering feedback button...');
		system.registerButton({
			id: 'step-feedback',
			label: 'Step Feedback',
			icon: <MessageSquare className="h-4 w-4" />,
			onClick: () => {
				addLog('Feedback button clicked!');
				alert('Feedback clicked!');
			},
			priority: 8,
		});

		addLog(`Buttons registered. Total: ${system.buttons.length}`);
		setStep(3);
	};

	// Step 3: Test controls
	const testControls = () => {
		addLog('Testing show/hide controls...');
		setStep(4);
	};

	return (
		<div className="min-h-screen p-8 space-y-8">
			<div className="space-y-4">
				<h1 className="text-3xl font-bold">Step by Step Test</h1>
				<p className="text-muted-foreground">
					Test floating system từng bước một để debug
				</p>
			</div>

			{/* Current Step */}
			<div className="p-4 border rounded-lg bg-primary/5">
				<h2 className="text-xl font-semibold mb-2">Step {step}</h2>
				{step === 1 && (
					<div className="space-y-2">
						<p>✅ Hook initialized</p>
						<p>System state: {JSON.stringify(system.systemState, null, 2)}</p>
						<Button onClick={() => setStep(2)}>Next: Register Buttons</Button>
					</div>
				)}
				{step === 2 && (
					<div className="space-y-2">
						<p>Ready to register buttons</p>
						<Button onClick={registerButtons}>Register Buttons</Button>
					</div>
				)}
				{step === 3 && (
					<div className="space-y-2">
						<p>✅ Buttons registered: {system.buttons.length}</p>
						<p>Check bottom-right corner for buttons</p>
						<Button onClick={testControls}>Next: Test Controls</Button>
					</div>
				)}
				{step === 4 && (
					<div className="space-y-4">
						<p>Test controls:</p>
						<div className="flex flex-wrap gap-2">
							<Button 
								size="sm"
								onClick={() => {
									system.showAllButtons();
									addLog('Show all buttons called');
								}}
							>
								Show All
							</Button>
							<Button 
								size="sm"
								onClick={() => {
									system.hideAllButtons();
									addLog('Hide all buttons called');
								}}
							>
								Hide All
							</Button>
							<Button 
								size="sm"
								onClick={() => {
									system.showButton('step-settings');
									addLog('Show settings called');
								}}
							>
								Show Settings
							</Button>
							<Button 
								size="sm"
								onClick={() => {
									system.hideButton('step-settings');
									addLog('Hide settings called');
								}}
							>
								Hide Settings
							</Button>
						</div>
					</div>
				)}
			</div>

			{/* System Info */}
			<div className="grid grid-cols-1 md:grid-cols-2 gap-4">
				<div className="p-4 border rounded-lg">
					<h3 className="font-semibold mb-2">System Info</h3>
					<div className="text-sm space-y-1">
						<div>Total buttons: {system.buttons.length}</div>
						<div>Visible buttons: {system.systemState.visibleCount}</div>
						<div>Position: {system.systemState.position}</div>
						<div>Gap: {system.systemState.gap}</div>
					</div>
				</div>
				<div className="p-4 border rounded-lg">
					<h3 className="font-semibold mb-2">Buttons</h3>
					<div className="text-sm space-y-1">
						{system.buttons.map((button) => (
							<div key={button.id} className="p-2 bg-muted/20 rounded">
								<div>ID: {button.id}</div>
								<div>Priority: {button.priority}</div>
								<div>Visible: {button.visible ? 'Yes' : 'No'}</div>
							</div>
						))}
					</div>
				</div>
			</div>

			{/* Logs */}
			<div className="space-y-4">
				<h3 className="text-lg font-semibold">Logs</h3>
				<div className="bg-black text-green-400 p-4 rounded-lg max-h-64 overflow-y-auto font-mono text-sm">
					{logs.length === 0 ? (
						<div>No logs yet...</div>
					) : (
						logs.map((log, index) => (
							<div key={index}>{log}</div>
						))
					)}
				</div>
				<Button 
					variant="outline" 
					size="sm"
					onClick={() => setLogs([])}
				>
					Clear Logs
				</Button>
			</div>

			{/* Expected Results */}
			<div className="space-y-4">
				<h3 className="text-lg font-semibold">Expected Results</h3>
				<ul className="space-y-1 text-sm list-disc list-inside">
					<li>Step 1: Hook should initialize with 0 buttons</li>
					<li>Step 2: After registering, should have 2 buttons</li>
					<li>Step 3: Buttons should appear at bottom-right corner</li>
					<li>Step 4: Controls should show/hide buttons correctly</li>
					<li>Settings button should be at bottom (priority 10)</li>
					<li>Feedback button should be above settings (priority 8)</li>
				</ul>
			</div>

			{/* Floating Button Manager */}
			{step >= 3 && (
				<FloatingButtonManager
					systemId="step-test"
					position="bottom-right"
					gap={2}
					maxVisible={5}
				/>
			)}
		</div>
	);
}
