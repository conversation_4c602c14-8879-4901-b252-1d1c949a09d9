'use client';

import React, { useState } from 'react';
import { Button } from '@/components/ui';
import {
	IntegratedFloatingButtons,
	FloatingButtonManager,
	useFloatingButtonSystem,
	FloatingButtonStack,
} from '@/components/floating-ui';
import { Plus, Heart, Share, Download, Info } from 'lucide-react';

export default function TestFloatingPage() {
	const [testResults, setTestResults] = useState<string[]>([]);

	const addTestResult = (result: string) => {
		setTestResults((prev) => [...prev, `${new Date().toLocaleTimeString()}: ${result}`]);
	};

	// Test system for manual buttons
	const testSystem = useFloatingButtonSystem('test-system', {
		position: 'bottom-left',
		gap: 2,
		maxVisible: 5,
	});

	// Register test buttons
	React.useEffect(() => {
		testSystem.registerButton({
			id: 'test-info',
			label: 'Test Info',
			icon: <Info className="h-4 w-4" />,
			onClick: () => addTestResult('Test Info button clicked'),
			priority: 5,
		});

		testSystem.registerButton({
			id: 'test-heart',
			label: 'Test Heart',
			icon: <Heart className="h-4 w-4" />,
			onClick: () => addTestResult('Test Heart button clicked'),
			priority: 3,
		});

		return () => {
			testSystem.unregisterButton('test-info');
			testSystem.unregisterButton('test-heart');
		};
	}, [testSystem]);

	return (
		<div className="min-h-screen p-8 space-y-8">
			<div className="space-y-4">
				<h1 className="text-3xl font-bold">Floating System Test Page</h1>
				<p className="text-muted-foreground">
					Test các chức năng và vị trí của floating system mới
				</p>
			</div>

			{/* Test Results */}
			<div className="space-y-4">
				<h2 className="text-xl font-semibold">Test Results</h2>
				<div className="bg-muted/20 p-4 rounded-lg max-h-64 overflow-y-auto">
					{testResults.length === 0 ? (
						<p className="text-muted-foreground">Chưa có test results...</p>
					) : (
						<ul className="space-y-1 text-sm">
							{testResults.map((result, index) => (
								<li key={index} className="font-mono">
									{result}
								</li>
							))}
						</ul>
					)}
				</div>
				<Button variant="outline" size="sm" onClick={() => setTestResults([])}>
					Clear Results
				</Button>
			</div>

			{/* Test Controls */}
			<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
				{/* Test 1: Integrated System */}
				<div className="space-y-4 p-4 border rounded-lg">
					<h3 className="font-semibold">Test 1: Integrated System</h3>
					<p className="text-sm text-muted-foreground">
						Settings (priority 10) và Feedback (priority 8) ở bottom-right
					</p>
					<div className="space-y-2">
						<Button
							size="sm"
							onClick={() =>
								addTestResult('Integrated system should be visible at bottom-right')
							}
						>
							Check Position
						</Button>
					</div>
				</div>

				{/* Test 2: Manual System */}
				<div className="space-y-4 p-4 border rounded-lg">
					<h3 className="font-semibold">Test 2: Manual System</h3>
					<p className="text-sm text-muted-foreground">
						Test buttons ở bottom-left với FloatingButtonManager
					</p>
					<div className="space-y-2">
						<Button size="sm" onClick={() => testSystem.showAllButtons()}>
							Show All
						</Button>
						<Button
							size="sm"
							variant="outline"
							onClick={() => testSystem.hideAllButtons()}
						>
							Hide All
						</Button>
					</div>
				</div>

				{/* Test 3: Stack System */}
				<div className="space-y-4 p-4 border rounded-lg">
					<h3 className="font-semibold">Test 3: Stack System</h3>
					<p className="text-sm text-muted-foreground">
						Manual stack ở top-right với FloatingButtonStack
					</p>
					<div className="space-y-2">
						<Button
							size="sm"
							onClick={() =>
								addTestResult('Stack system should be visible at top-right')
							}
						>
							Check Stack
						</Button>
					</div>
				</div>
			</div>

			{/* Test Instructions */}
			<div className="space-y-4">
				<h2 className="text-xl font-semibold">Test Instructions</h2>
				<div className="grid grid-cols-1 md:grid-cols-2 gap-6">
					<div className="space-y-2">
						<h3 className="font-medium">Visual Tests:</h3>
						<ul className="text-sm space-y-1 text-muted-foreground">
							<li>1. Check bottom-right: Settings button (gần bottom nhất)</li>
							<li>2. Check bottom-right: Feedback button (phía trên settings)</li>
							<li>3. Check bottom-left: Test buttons (Info, Heart)</li>
							<li>4. Check top-right: Manual stack buttons</li>
							<li>5. Verify consistent spacing between buttons</li>
						</ul>
					</div>
					<div className="space-y-2">
						<h3 className="font-medium">Functional Tests:</h3>
						<ul className="text-sm space-y-1 text-muted-foreground">
							<li>1. Click Settings button → Should open modal</li>
							<li>2. Click Feedback button → Should open modal</li>
							<li>3. Click test buttons → Should log to results</li>
							<li>4. Test show/hide functionality</li>
							<li>5. Test responsive behavior</li>
						</ul>
					</div>
				</div>
			</div>

			{/* System Status */}
			<div className="space-y-4">
				<h2 className="text-xl font-semibold">System Status</h2>
				<div className="grid grid-cols-1 md:grid-cols-3 gap-4">
					<div className="p-4 border rounded-lg">
						<h4 className="font-medium">Test System</h4>
						<p className="text-sm text-muted-foreground">
							Buttons: {testSystem.buttons.length}
						</p>
						<p className="text-sm text-muted-foreground">
							Position: {testSystem.systemState.position}
						</p>
					</div>
					<div className="p-4 border rounded-lg">
						<h4 className="font-medium">Integrated System</h4>
						<p className="text-sm text-muted-foreground">Settings + Feedback buttons</p>
						<p className="text-sm text-muted-foreground">Position: bottom-right</p>
					</div>
					<div className="p-4 border rounded-lg">
						<h4 className="font-medium">Stack System</h4>
						<p className="text-sm text-muted-foreground">Manual buttons</p>
						<p className="text-sm text-muted-foreground">Position: top-right</p>
					</div>
				</div>
			</div>

			{/* Floating Systems */}

			{/* 1. Integrated System (bottom-right) */}
			<IntegratedFloatingButtons
				systemId="integrated-test"
				position="bottom-right"
				gap={2}
				includeSettings={true}
				includeFeedback={true}
			/>

			{/* 2. Manual System (bottom-left) */}
			<FloatingButtonManager
				systemId="test-system"
				position="bottom-left"
				gap={2}
				maxVisible={5}
			/>

			{/* 3. Stack System (top-right) */}
			<FloatingButtonStack position="top-right" gap={2}>
				<Button
					size="icon"
					variant="outline"
					className="h-12 w-12 rounded-full shadow-md bg-background"
					onClick={() => addTestResult('Manual Share button clicked')}
				>
					<Share className="h-4 w-4" />
				</Button>
				<Button
					size="icon"
					variant="outline"
					className="h-12 w-12 rounded-full shadow-md bg-background"
					onClick={() => addTestResult('Manual Download button clicked')}
				>
					<Download className="h-4 w-4" />
				</Button>
				<Button
					size="icon"
					className="h-14 w-14 rounded-full shadow-lg"
					onClick={() => addTestResult('Manual Plus button clicked')}
				>
					<Plus className="h-6 w-6" />
				</Button>
			</FloatingButtonStack>
		</div>
	);
}
