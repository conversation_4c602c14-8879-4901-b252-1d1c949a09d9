'use client';

import React from 'react';
import { Button } from '@/components/ui';
import {
	FloatingButtonManager,
	FloatingButtonStack,
	useFloatingButtonSystem,
	useFloatingButton,
} from '@/components/floating-ui';
import { Plus, Settings, Info, Heart, Share, Download, Upload } from 'lucide-react';

// ============================================================================
// FLOATING BUTTON DEMO COMPONENT
// ============================================================================

export function FloatingButtonDemo() {
	const system = useFloatingButtonSystem('demo-system', {
		position: 'bottom-right',
		gap: 3,
		maxVisible: 5,
		collisionDetection: true,
	});

	// Register some demo buttons
	React.useEffect(() => {
		const buttons = [
			{
				id: 'settings',
				label: 'Settings',
				icon: <Settings className="h-4 w-4" />,
				onClick: () => console.log('Settings clicked'),
				priority: 10,
			},
			{
				id: 'info',
				label: 'Information',
				icon: <Info className="h-4 w-4" />,
				onClick: () => console.log('Info clicked'),
				priority: 8,
			},
			{
				id: 'favorite',
				label: 'Add to Favorites',
				icon: <Heart className="h-4 w-4" />,
				onClick: () => console.log('Favorite clicked'),
				priority: 6,
			},
			{
				id: 'share',
				label: 'Share',
				icon: <Share className="h-4 w-4" />,
				onClick: () => console.log('Share clicked'),
				priority: 4,
			},
			{
				id: 'download',
				label: 'Download',
				icon: <Download className="h-4 w-4" />,
				onClick: () => console.log('Download clicked'),
				priority: 2,
			},
		];

		buttons.forEach((button) => {
			system.registerButton(button);
		});

		return () => {
			buttons.forEach((button) => {
				system.unregisterButton(button.id);
			});
		};
	}, [system]);

	return (
		<div className="p-8 space-y-6">
			<div className="space-y-4">
				<h2 className="text-2xl font-bold">Floating Button System Demo</h2>
				<p className="text-muted-foreground">
					This demo shows the new floating button system with priority-based ordering and flex vertical layout.
				</p>
			</div>

			<div className="grid grid-cols-1 md:grid-cols-2 gap-6">
				{/* Control Panel */}
				<div className="space-y-4">
					<h3 className="text-lg font-semibold">Controls</h3>
					<div className="grid grid-cols-2 gap-2">
						<Button
							variant="outline"
							size="sm"
							onClick={() => system.showButton('settings')}
						>
							Show Settings
						</Button>
						<Button
							variant="outline"
							size="sm"
							onClick={() => system.hideButton('settings')}
						>
							Hide Settings
						</Button>
						<Button
							variant="outline"
							size="sm"
							onClick={() => system.showAllButtons()}
						>
							Show All
						</Button>
						<Button
							variant="outline"
							size="sm"
							onClick={() => system.hideAllButtons()}
						>
							Hide All
						</Button>
					</div>
				</div>

				{/* System State */}
				<div className="space-y-4">
					<h3 className="text-lg font-semibold">System State</h3>
					<div className="text-sm space-y-2">
						<div>Total Buttons: {system.systemState.totalButtons}</div>
						<div>Visible Buttons: {system.systemState.visibleCount}</div>
						<div>Position: {system.systemState.position}</div>
						<div>Gap: {system.systemState.gap}</div>
					</div>
				</div>
			</div>

			{/* Floating Button Manager */}
			<FloatingButtonManager
				systemId="demo-system"
				position="bottom-right"
				gap={3}
				maxVisible={5}
				collisionDetection={true}
			/>
		</div>
	);
}

// ============================================================================
// FLOATING BUTTON STACK DEMO
// ============================================================================

export function FloatingButtonStackDemo() {
	return (
		<div className="p-8 space-y-6">
			<div className="space-y-4">
				<h2 className="text-2xl font-bold">Floating Button Stack Demo</h2>
				<p className="text-muted-foreground">
					This demo shows the FloatingButtonStack component with manual button management.
				</p>
			</div>

			{/* Manual Stack */}
			<FloatingButtonStack
				position="bottom-left"
				gap={2}
				className="demo-stack"
			>
				<Button
					size="icon"
					variant="outline"
					className="h-12 w-12 rounded-full shadow-md bg-background"
					onClick={() => console.log('Upload clicked')}
				>
					<Upload className="h-4 w-4" />
				</Button>
				<Button
					size="icon"
					variant="outline"
					className="h-12 w-12 rounded-full shadow-md bg-background"
					onClick={() => console.log('Download clicked')}
				>
					<Download className="h-4 w-4" />
				</Button>
				<Button
					size="icon"
					className="h-14 w-14 rounded-full shadow-lg"
					onClick={() => console.log('Main action clicked')}
				>
					<Plus className="h-6 w-6" />
				</Button>
			</FloatingButtonStack>
		</div>
	);
}

// ============================================================================
// INDIVIDUAL BUTTON HOOK DEMO
// ============================================================================

function IndividualButtonDemo() {
	const settingsButton = useFloatingButton(
		'individual-settings',
		{
			label: 'Settings',
			icon: <Settings className="h-4 w-4" />,
			onClick: () => console.log('Individual settings clicked'),
			priority: 10,
		},
		'individual-system'
	);

	const infoButton = useFloatingButton(
		'individual-info',
		{
			label: 'Info',
			icon: <Info className="h-4 w-4" />,
			onClick: () => console.log('Individual info clicked'),
			priority: 5,
		},
		'individual-system'
	);

	return (
		<div className="p-8 space-y-6">
			<div className="space-y-4">
				<h2 className="text-2xl font-bold">Individual Button Hook Demo</h2>
				<p className="text-muted-foreground">
					This demo shows individual button management using the useFloatingButton hook.
				</p>
			</div>

			<div className="space-x-4">
				<Button
					variant="outline"
					onClick={settingsButton.toggle}
				>
					{settingsButton.isVisible ? 'Hide' : 'Show'} Settings
				</Button>
				<Button
					variant="outline"
					onClick={infoButton.toggle}
				>
					{infoButton.isVisible ? 'Hide' : 'Show'} Info
				</Button>
			</div>

			{/* Button Manager for individual system */}
			<FloatingButtonManager
				systemId="individual-system"
				position="top-right"
				gap={2}
			/>
		</div>
	);
}

export { IndividualButtonDemo };
