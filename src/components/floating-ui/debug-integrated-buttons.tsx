'use client';

import React, { useState } from 'react';
import { Button } from '@/components/ui';
import { Settings, MessageSquare, X } from 'lucide-react';

export function DebugIntegratedButtons() {
	console.log('🔍 DebugIntegratedButtons rendering...');
	
	const [isSettingsOpen, setIsSettingsOpen] = useState(false);
	const [isFeedbackOpen, setIsFeedbackOpen] = useState(false);

	return (
		<>
			{/* Fixed Floating Buttons */}
			<div className="fixed bottom-6 right-6 z-50 flex flex-col-reverse items-end gap-2">
				<Button
					size="icon"
					className="h-12 w-12 rounded-full shadow-md bg-background border hover:shadow-lg transition-all duration-200"
					onClick={() => {
						console.log('Settings button clicked!');
						setIsSettingsOpen(true);
					}}
					aria-label="Settings"
				>
					<Settings className="h-4 w-4" />
				</Button>
				<Button
					size="icon"
					className="h-12 w-12 rounded-full shadow-md bg-background border hover:shadow-lg transition-all duration-200"
					onClick={() => {
						console.log('Feedback button clicked!');
						setIsFeedbackOpen(true);
					}}
					aria-label="Feedback"
				>
					<MessageSquare className="h-4 w-4" />
				</Button>
			</div>

			{/* Settings Modal */}
			{isSettingsOpen && (
				<div className="fixed inset-0 z-[1300] flex items-center justify-center">
					<div
						className="absolute inset-0 bg-black/20"
						onClick={() => setIsSettingsOpen(false)}
					/>
					<div className="relative z-10 bg-white rounded-lg shadow-lg p-6 max-w-md w-full mx-4">
						<div className="flex items-center justify-between mb-4">
							<h2 className="text-lg font-semibold">Settings</h2>
							<Button
								size="icon"
								variant="ghost"
								onClick={() => setIsSettingsOpen(false)}
								aria-label="Close"
							>
								<X className="h-4 w-4" />
							</Button>
						</div>
						<div className="space-y-4">
							<p>Settings content goes here...</p>
							<div className="flex justify-end gap-2">
								<Button
									variant="outline"
									onClick={() => setIsSettingsOpen(false)}
								>
									Cancel
								</Button>
								<Button onClick={() => setIsSettingsOpen(false)}>
									Save
								</Button>
							</div>
						</div>
					</div>
				</div>
			)}

			{/* Feedback Modal */}
			{isFeedbackOpen && (
				<div className="fixed inset-0 z-[1300] flex items-center justify-center p-4">
					<div
						className="absolute inset-0 bg-black/20"
						onClick={() => setIsFeedbackOpen(false)}
					/>
					<div className="relative z-10 bg-white rounded-lg shadow-lg p-6 max-w-md w-full">
						<div className="flex items-center justify-between mb-4">
							<h2 className="text-lg font-semibold">Feedback</h2>
							<Button
								size="icon"
								variant="ghost"
								onClick={() => setIsFeedbackOpen(false)}
								aria-label="Close"
							>
								<X className="h-4 w-4" />
							</Button>
						</div>
						<div className="space-y-4">
							<textarea
								className="w-full p-3 border rounded-md resize-none"
								rows={4}
								placeholder="Share your feedback..."
							/>
							<div className="flex justify-end gap-2">
								<Button
									variant="outline"
									onClick={() => setIsFeedbackOpen(false)}
								>
									Cancel
								</Button>
								<Button onClick={() => setIsFeedbackOpen(false)}>
									Send
								</Button>
							</div>
						</div>
					</div>
				</div>
			)}
		</>
	);
}
