'use client';

import React from 'react';
import { Button } from '@/components/ui';
import { cn } from '@/lib';
import {
	useFloatingButtonSystem,
	FloatingButtonSystemOptions,
} from '@/hooks/use-floating-button-system';
import { useFloatingUIElement } from '@/hooks/use-floating-ui';

// ============================================================================
// TYPES
// ============================================================================

interface FloatingButtonManagerProps extends FloatingButtonSystemOptions {
	systemId?: string;
	className?: string;
	style?: React.CSSProperties;
}

interface FloatingButtonRendererProps {
	systemId: string;
	options: FloatingButtonSystemOptions;
}

// ============================================================================
// FLOATING BUTTON RENDERER
// ============================================================================

function FloatingButtonRenderer({ systemId, options }: FloatingButtonRendererProps) {
	const system = useFloatingButtonSystem(systemId, options);
	const { buttons, systemState } = system;

	// Create floating UI elements for each button
	const buttonElements = buttons.map((button, index) => {
		const position = systemState.positions[index];

		const buttonContent = (
			<Button
				size="icon"
				variant={button.disabled ? 'ghost' : 'default'}
				disabled={button.disabled}
				className={cn(
					'h-12 w-12 rounded-full shadow-md bg-background border',
					'hover:shadow-lg transition-all duration-200',
					'focus:ring-2 focus:ring-primary focus:ring-offset-2',
					button.className
				)}
				onClick={button.onClick}
				aria-label={button.label}
			>
				{button.icon}
			</Button>
		);

		return {
			id: `${systemId}-button-${button.id}`,
			content: buttonContent,
			coordinates: position,
			button,
		};
	});

	// Register each button as a floating UI element
	buttonElements.forEach(({ id, content, coordinates }) => {
		useFloatingUIElement(id, content, {
			type: 'custom',
			priority: 'medium',
			position: 'custom',
			coordinates,
			animation: { type: 'scale', duration: 200 },
			autoShow: true,
			collisionDetection: options.collisionDetection !== false,
			className: 'floating-button-element',
		});
	});

	return null; // Content is rendered through floating UI system
}

// ============================================================================
// FLOATING BUTTON MANAGER
// ============================================================================

export function FloatingButtonManager({
	systemId = 'default',
	position = 'bottom-right',
	gap = 2,
	maxVisible = 10,
	autoHide = false,
	collisionDetection = true,
	className,
	style,
}: FloatingButtonManagerProps) {
	const options: FloatingButtonSystemOptions = {
		position,
		gap,
		maxVisible,
		autoHide,
		collisionDetection,
	};

	return (
		<div className={cn('floating-button-manager', className)} style={style}>
			<FloatingButtonRenderer systemId={systemId} options={options} />
		</div>
	);
}

// ============================================================================
// FLOATING BUTTON STACK COMPONENT
// ============================================================================

interface FloatingButtonStackProps {
	systemId?: string;
	position?: 'bottom-right' | 'bottom-left' | 'top-right' | 'top-left';
	gap?: number;
	className?: string;
	children?: React.ReactNode;
}

export function FloatingButtonStack({
	systemId = 'default',
	position = 'bottom-right',
	gap = 2,
	className,
	children,
}: FloatingButtonStackProps) {
	const system = useFloatingButtonSystem(systemId, { position, gap });
	const { buttons } = system;

	const positionClasses = {
		'bottom-right': 'bottom-6 right-6',
		'bottom-left': 'bottom-6 left-6',
		'top-right': 'top-6 right-6',
		'top-left': 'top-6 left-6',
	};

	const flexDirection = position.startsWith('bottom') ? 'flex-col-reverse' : 'flex-col';
	const alignItems = position.endsWith('right') ? 'items-end' : 'items-start';

	if (buttons.length === 0 && !children) {
		return null;
	}

	return (
		<div
			className={cn(
				'fixed z-50 flex',
				flexDirection,
				alignItems,
				positionClasses[position],
				className
			)}
			style={{ gap: `${gap * 0.25}rem` }}
		>
			{/* Render system buttons */}
			{buttons.map((button) => (
				<Button
					key={button.id}
					size="icon"
					variant={button.disabled ? 'ghost' : 'outline'}
					disabled={button.disabled}
					className={cn(
						'h-12 w-12 rounded-full shadow-md bg-background',
						'hover:shadow-lg transition-all duration-200',
						'focus:ring-2 focus:ring-primary focus:ring-offset-2',
						button.className
					)}
					onClick={button.onClick}
					aria-label={button.label}
				>
					{button.icon}
				</Button>
			))}

			{/* Render additional children */}
			{children}
		</div>
	);
}

// ============================================================================
// FLOATING BUTTON WRAPPER (Updated)
// ============================================================================

interface FloatingButtonWrapperProps {
	children: React.ReactNode;
	className?: string;
	position?: 'bottom-right' | 'bottom-left' | 'top-right' | 'top-left';
	gap?: number;
	systemId?: string;
	integrateWithSystem?: boolean;
}

export function FloatingButtonWrapper({
	children,
	className,
	position = 'bottom-right',
	gap = 2,
	systemId = 'default',
	integrateWithSystem = false,
}: FloatingButtonWrapperProps) {
	if (integrateWithSystem) {
		return (
			<FloatingButtonStack
				systemId={systemId}
				position={position}
				gap={gap}
				className={className}
			>
				{children}
			</FloatingButtonStack>
		);
	}

	// Original wrapper behavior
	const positionClasses = {
		'bottom-right': 'bottom-6 right-6',
		'bottom-left': 'bottom-6 left-6',
		'top-right': 'top-6 right-6',
		'top-left': 'top-6 left-6',
	};

	const flexDirection = position.startsWith('bottom') ? 'flex-col-reverse' : 'flex-col';
	const alignItems = position.endsWith('right') ? 'items-end' : 'items-start';

	return (
		<div
			className={cn(
				'fixed z-50 flex',
				flexDirection,
				alignItems,
				positionClasses[position],
				className
			)}
			style={{ gap: `${gap * 0.25}rem` }}
		>
			{children}
		</div>
	);
}

// ============================================================================
// EXPORTS
// ============================================================================

export { useFloatingButtonSystem, useFloatingButton } from '@/hooks/use-floating-button-system';
export type {
	FloatingButtonItem,
	FloatingButtonSystemOptions,
} from '@/hooks/use-floating-button-system';
