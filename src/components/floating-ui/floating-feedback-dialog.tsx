'use client';

import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, Textarea, Translate, useToast } from '@/components/ui';
import { FEEDBACK_SECTION_LOADING_KEYS } from '@/constants/loading-keys';
import { LOADING_SCOPES } from '@/constants/loading-scopes';
import { useScopedLoading, useTranslation } from '@/contexts';
import { useFloatingUIElement } from '@/hooks/use-floating-ui';
import { motion } from 'framer-motion';
import { MessageSquare, Send, X } from 'lucide-react';
import { FormEvent, useState, useEffect } from 'react';

interface FloatingFeedbackProps {
	id?: string;
	className?: string;
}

export function FloatingFeedback({ id = 'floating-feedback', className }: FloatingFeedbackProps) {
	const [isOpen, setIsOpen] = useState(false);
	const [feedbackMessage, setFeedbackMessage] = useState('');
	const { setLoading: setSubmittingLoading, getLoading: getSubmittingLoading } = useScopedLoading(
		LOADING_SCOPES.FEEDBACK_SECTION
	);
	const { t } = useTranslation();
	const { toast } = useToast();

	const handleFeedbackSubmit = async (e: FormEvent) => {
		e.preventDefault();

		// Validate required fields
		if (!feedbackMessage) {
			toast({
				title: t('toast.missing_info'),
				description: t('toast.missing_info_desc'),
			});
			return;
		}

		setSubmittingLoading(FEEDBACK_SECTION_LOADING_KEYS.SUBMIT_FEEDBACK, true);

		try {
			const response = await fetch('/api/feedback', {
				method: 'POST',
				headers: {
					'Content-Type': 'application/json',
				},
				body: JSON.stringify({ message: feedbackMessage }),
			});

			if (!response.ok) {
				const errorData = await response.json();
				throw new Error(errorData.error || 'Failed to submit feedback');
			}

			toast({
				title: t('toast.feedback_sent'),
				description: t('toast.feedback_sent_desc'),
			});
			setFeedbackMessage('');
			setIsOpen(false);
		} catch (error) {
			toast({
				variant: 'destructive',
				title: t('toast.submission_failed'),
				description:
					error instanceof Error ? error.message : t('toast.submission_failed_desc'),
			});
		} finally {
			setSubmittingLoading(FEEDBACK_SECTION_LOADING_KEYS.SUBMIT_FEEDBACK, false);
		}
	};

	// Feedback button content
	const feedbackButton = (
		<Button
			size="icon"
			className="h-14 w-14 rounded-full shadow-lg hover:shadow-xl transition-all duration-200 hover:scale-105 bg-gradient-to-r from-primary to-primary/80"
			onClick={() => setIsOpen(true)}
			title={t('feedback.button_title')}
			aria-label={t('feedback.button_title')}
		>
			<MessageSquare className="h-6 w-6" />
		</Button>
	);

	// Feedback panel content
	const feedbackPanel = (
		<div className="bg-background border rounded-lg shadow-lg p-6 w-full max-w-sm">
			{/* Header */}
			<div className="flex items-center justify-between mb-4">
				<h3 className="text-lg font-semibold flex items-center gap-2">
					<div className="p-2 rounded-lg bg-primary text-primary-foreground">
						<MessageSquare className="h-4 w-4" />
					</div>
					<Translate text="feedback.title" />
				</h3>
				<Button
					variant="ghost"
					size="sm"
					onClick={() => setIsOpen(false)}
					className="h-8 w-8 p-0 hover:bg-muted"
				>
					<X className="h-4 w-4" />
				</Button>
			</div>

			{/* Form */}
			<motion.form
				initial={{ opacity: 0, y: -10 }}
				animate={{ opacity: 1, y: 0 }}
				transition={{ duration: 0.3 }}
				className="space-y-4"
				onSubmit={handleFeedbackSubmit}
			>
				<div>
					<label htmlFor="feedback" className="block text-sm font-medium mb-2">
						<Translate text="feedback.message" />
					</label>
					<Textarea
						id="feedback"
						value={feedbackMessage}
						onChange={(e) => setFeedbackMessage(e.target.value)}
						className="w-full min-h-[100px] focus:ring-2 focus:ring-primary/20 transition-all duration-200"
						placeholder={t('feedback.placeholder.message')}
						required
					/>
				</div>
				<div className="flex justify-end gap-2">
					<Button
						type="button"
						variant="outline"
						onClick={() => setIsOpen(false)}
						disabled={getSubmittingLoading('submitFeedback')}
						size="sm"
					>
						<Translate text="common.cancel" />
					</Button>
					<Button
						type="submit"
						disabled={getSubmittingLoading('submitFeedback')}
						className="flex items-center gap-2"
						size="sm"
					>
						{getSubmittingLoading('submitFeedback') ? (
							<LoadingSpinner size="sm" />
						) : (
							<Send className="h-4 w-4" />
						)}
						<Translate text="feedback.submit" />
					</Button>
				</div>
			</motion.form>
		</div>
	);

	// Use floating UI element for button - visible when not open
	const { show: showButton, hide: hideButton } = useFloatingUIElement(
		`${id}-button`,
		feedbackButton,
		{
			type: 'custom',
			priority: 'medium',
			position: 'bottom-right',
			coordinates: { bottom: 144, right: 16 }, // 80 (guidance) + 64 (button height) = 144
			animation: { type: 'scale', duration: 200 },
			autoShow: true,
			className,
			collisionDetection: true,
		}
	);

	// Use floating UI element for panel - visible when open
	const { show: showPanel, hide: hidePanel } = useFloatingUIElement(
		`${id}-panel`,
		feedbackPanel,
		{
			type: 'modal',
			priority: 'high',
			position: 'bottom-right',
			coordinates: { bottom: 144, right: 16 },
			animation: { type: 'scale', duration: 300 },
			autoShow: false,
			className,
			persistent: true,
			style: {
				backgroundColor: 'rgba(0, 0, 0, 0.1)',
			},
		}
	);

	// Update visibility when isOpen changes
	useEffect(() => {
		if (isOpen) {
			hideButton();
			showPanel();
		} else {
			hidePanel();
			showButton();
		}
	}, [isOpen, showButton, hideButton, showPanel, hidePanel]);

	return null; // Content is rendered through floating UI system
}
