'use client';

import React, { useState, useMemo, useEffect } from 'react';
import { Button, useTheme, Textarea } from '@/components/ui';
import { useScopedLoading, useTranslation } from '@/contexts';
import { useToast } from '@/components/ui/use-toast';
import { useAuth } from '@/contexts/auth-context';
import { getTranslationKeyOfLanguage } from '@/contexts/translations';
import { useFloatingButtonSystem } from '@/hooks/use-floating-button-system';
import { FloatingButtonManager } from './floating-button-manager';
import {
	Settings,
	MessageSquare,
	Languages,
	LogOut,
	Monitor,
	Moon,
	Sun,
	User,
	X,
} from 'lucide-react';
import { Language } from '@prisma/client';
import { useRouter } from 'next/navigation';
import { motion } from 'framer-motion';
import { LOADING_SCOPES, FEEDBACK_SECTION_LOADING_KEYS } from '@/constants';

// ============================================================================
// INTEGRATED FLOATING BUTTONS COMPONENT
// ============================================================================

interface IntegratedFloatingButtonsProps {
	systemId?: string;
	position?: 'bottom-right' | 'bottom-left' | 'top-right' | 'top-left';
	gap?: number;
	includeSettings?: boolean;
	includeFeedback?: boolean;
}

export function IntegratedFloatingButtons({
	systemId = 'integrated-system',
	position = 'bottom-right',
	gap = 2,
	includeSettings = true,
	includeFeedback = true,
}: IntegratedFloatingButtonsProps) {
	const { t } = useTranslation();
	const { user, logout } = useAuth();
	const { language, setLanguage } = useTranslation();
	const { theme, setTheme } = useTheme();
	const router = useRouter();
	const { toast } = useToast();
	const { setLoading: setSubmittingLoading, getLoading: getSubmittingLoading } = useScopedLoading(
		LOADING_SCOPES.FEEDBACK_SECTION
	);

	// State for modals
	const [isSettingsOpen, setIsSettingsOpen] = useState(false);
	const [isFeedbackOpen, setIsFeedbackOpen] = useState(false);
	const [feedbackMessage, setFeedbackMessage] = useState('');

	const system = useFloatingButtonSystem(systemId, {
		position,
		gap,
		maxVisible: 10,
		collisionDetection: true,
	});

	// Handle logout
	const handleLogout = async () => {
		try {
			await logout();
			router.push('/');
		} catch (error) {
			console.error('Logout failed:', error);
		}
	};

	// Handle feedback submit
	const handleFeedbackSubmit = async (e: React.FormEvent) => {
		e.preventDefault();

		if (!feedbackMessage) {
			toast({
				title: t('toast.missing_info'),
				description: t('toast.missing_info_desc'),
			});
			return;
		}

		setSubmittingLoading(FEEDBACK_SECTION_LOADING_KEYS.SUBMIT_FEEDBACK, true);

		try {
			const response = await fetch('/api/feedback', {
				method: 'POST',
				headers: {
					'Content-Type': 'application/json',
				},
				body: JSON.stringify({ message: feedbackMessage }),
			});

			if (!response.ok) {
				const errorData = await response.json();
				throw new Error(errorData.error || 'Failed to submit feedback');
			}

			toast({
				title: t('toast.feedback_sent'),
				description: t('toast.feedback_sent_desc'),
			});
			setFeedbackMessage('');
			setIsFeedbackOpen(false);
		} catch (error) {
			toast({
				variant: 'destructive',
				title: t('toast.submission_failed'),
				description:
					error instanceof Error ? error.message : t('toast.submission_failed_desc'),
			});
		} finally {
			setSubmittingLoading(FEEDBACK_SECTION_LOADING_KEYS.SUBMIT_FEEDBACK, false);
		}
	};

	// Settings panel content
	const settingsPanel = useMemo(
		() => (
			<div className="bg-background border rounded-lg shadow-lg w-64 p-4">
				<div className="flex items-center justify-between mb-4">
					<h3 className="text-lg font-semibold">{t('settings.title')}</h3>
					<Button
						size="icon"
						variant="ghost"
						className="h-8 w-8"
						onClick={() => setIsSettingsOpen(false)}
					>
						<X className="h-4 w-4" />
					</Button>
				</div>

				{/* User Section */}
				{user && (
					<div className="mb-4 pb-4 border-b">
						<div className="flex items-center gap-2 mb-2">
							<User className="h-4 w-4" />
							<span className="text-sm font-medium">{user.email}</span>
						</div>
						<Button
							variant="outline"
							size="sm"
							className="w-full justify-start"
							onClick={handleLogout}
						>
							<LogOut className="h-4 w-4 mr-2" />
							{t('auth.logout')}
						</Button>
					</div>
				)}

				{/* Language Section */}
				<div className="mb-4">
					<div className="flex items-center gap-2 mb-2">
						<Languages className="h-4 w-4" />
						<span className="text-sm font-medium">{t('settings.language')}</span>
					</div>
					<div className="space-y-1">
						<Button
							variant={language === Language.EN ? 'default' : 'ghost'}
							size="sm"
							className="w-full justify-start"
							onClick={() => setLanguage(Language.EN)}
						>
							{t(getTranslationKeyOfLanguage(Language.EN))}
						</Button>
						<Button
							variant={language === Language.VI ? 'default' : 'ghost'}
							size="sm"
							className="w-full justify-start"
							onClick={() => setLanguage(Language.VI)}
						>
							{t(getTranslationKeyOfLanguage(Language.VI))}
						</Button>
					</div>
				</div>

				{/* Theme Section */}
				<div>
					<div className="flex items-center gap-2 mb-2">
						<Monitor className="h-4 w-4" />
						<span className="text-sm font-medium">{t('settings.theme')}</span>
					</div>
					<div className="space-y-1">
						<Button
							variant={theme === 'light' ? 'default' : 'ghost'}
							size="sm"
							className="w-full justify-start"
							onClick={() => setTheme('light')}
						>
							<Sun className="h-4 w-4 mr-2" />
							{t('theme.light')}
						</Button>
						<Button
							variant={theme === 'dark' ? 'default' : 'ghost'}
							size="sm"
							className="w-full justify-start"
							onClick={() => setTheme('dark')}
						>
							<Moon className="h-4 w-4 mr-2" />
							{t('theme.dark')}
						</Button>
						<Button
							variant={theme === 'system' ? 'default' : 'ghost'}
							size="sm"
							className="w-full justify-start"
							onClick={() => setTheme('system')}
						>
							<Monitor className="h-4 w-4 mr-2" />
							{t('theme.system')}
						</Button>
					</div>
				</div>
			</div>
		),
		[t, user, handleLogout, language, setLanguage, theme, setTheme]
	);

	// Feedback panel content
	const feedbackPanel = useMemo(
		() => (
			<div className="bg-background border rounded-lg shadow-lg p-6 w-full max-w-sm">
				<div className="flex items-center justify-between mb-4">
					<h3 className="text-lg font-semibold flex items-center gap-2">
						<div className="p-2 rounded-lg bg-primary text-primary-foreground">
							<MessageSquare className="h-4 w-4" />
						</div>
						{t('feedback.title')}
					</h3>
					<Button
						variant="ghost"
						size="sm"
						onClick={() => setIsFeedbackOpen(false)}
						className="h-8 w-8 p-0 hover:bg-muted"
					>
						<X className="h-4 w-4" />
					</Button>
				</div>

				<motion.form
					initial={{ opacity: 0, y: -10 }}
					animate={{ opacity: 1, y: 0 }}
					transition={{ duration: 0.3 }}
					className="space-y-4"
					onSubmit={handleFeedbackSubmit}
				>
					<div>
						<label htmlFor="feedback" className="block text-sm font-medium mb-2">
							{t('feedback.message')}
						</label>
						<Textarea
							id="feedback"
							value={feedbackMessage}
							onChange={(e) => setFeedbackMessage(e.target.value)}
							className="w-full min-h-[100px] focus:ring-2 focus:ring-primary/20 transition-all duration-200"
							placeholder={t('feedback.placeholder.message')}
							required
						/>
					</div>
					<div className="flex justify-end gap-2">
						<Button
							type="button"
							variant="outline"
							onClick={() => setIsFeedbackOpen(false)}
							disabled={getSubmittingLoading('submitFeedback')}
							size="sm"
						>
							Cancel
						</Button>
						<Button
							type="submit"
							disabled={getSubmittingLoading('submitFeedback')}
							size="sm"
						>
							{getSubmittingLoading('submitFeedback') ? 'Sending...' : 'Send'}
						</Button>
					</div>
				</motion.form>
			</div>
		),
		[t, feedbackMessage, setFeedbackMessage, handleFeedbackSubmit, getSubmittingLoading]
	);

	// Register buttons
	useEffect(() => {
		const { registerButton, unregisterButton } = system;

		if (includeSettings) {
			registerButton({
				id: 'integrated-settings',
				label: t('settings.button'),
				icon: <Settings className="h-6 w-6" />,
				onClick: () => setIsSettingsOpen(true),
				priority: 10, // Highest priority
			});
		}

		if (includeFeedback) {
			registerButton({
				id: 'integrated-feedback',
				label: t('feedback.button_title'),
				icon: <MessageSquare className="h-6 w-6" />,
				onClick: () => setIsFeedbackOpen(true),
				priority: 8, // Second priority
			});
		}

		return () => {
			if (includeSettings) {
				unregisterButton('integrated-settings');
			}
			if (includeFeedback) {
				unregisterButton('integrated-feedback');
			}
		};
		// eslint-disable-next-line react-hooks/exhaustive-deps
	}, [includeSettings, includeFeedback, t]);

	return (
		<>
			{/* Floating Button Manager */}
			<FloatingButtonManager
				systemId={systemId}
				position={position}
				gap={gap}
				maxVisible={10}
				collisionDetection={true}
			/>

			{/* Settings Modal */}
			{isSettingsOpen && (
				<div className="fixed inset-0 z-[1300] flex items-center justify-center">
					<div
						className="absolute inset-0 bg-black/20"
						onClick={() => setIsSettingsOpen(false)}
					/>
					<div className="relative z-10">{settingsPanel}</div>
				</div>
			)}

			{/* Feedback Modal */}
			{isFeedbackOpen && (
				<div className="fixed inset-0 z-[1300] flex items-center justify-center p-4">
					<div
						className="absolute inset-0 bg-black/20"
						onClick={() => setIsFeedbackOpen(false)}
					/>
					<div className="relative z-10">{feedbackPanel}</div>
				</div>
			)}
		</>
	);
}
