'use client';

import React, { useState } from 'react';
import { Button } from '@/components/ui';
import { Settings, MessageSquare, X } from 'lucide-react';
import { useTranslation } from '@/contexts/translation-context';

interface SimpleFloatingButtonsProps {
	includeSettings?: boolean;
	includeFeedback?: boolean;
}

export function SimpleFloatingButtons({ 
	includeSettings = true, 
	includeFeedback = true 
}: SimpleFloatingButtonsProps) {
	const { t } = useTranslation();
	const [isSettingsOpen, setIsSettingsOpen] = useState(false);
	const [isFeedbackOpen, setIsFeedbackOpen] = useState(false);

	return (
		<>
			{/* Fixed Floating Buttons */}
			<div className="fixed bottom-6 right-6 z-50 flex flex-col-reverse items-end gap-2">
				{includeSettings && (
					<Button
						size="icon"
						className="h-12 w-12 rounded-full shadow-md bg-background border hover:shadow-lg transition-all duration-200"
						onClick={() => setIsSettingsOpen(true)}
						aria-label={t('settings.button')}
					>
						<Settings className="h-4 w-4" />
					</Button>
				)}
				{includeFeedback && (
					<Button
						size="icon"
						className="h-12 w-12 rounded-full shadow-md bg-background border hover:shadow-lg transition-all duration-200"
						onClick={() => setIsFeedbackOpen(true)}
						aria-label={t('feedback.button_title')}
					>
						<MessageSquare className="h-4 w-4" />
					</Button>
				)}
			</div>

			{/* Settings Modal */}
			{isSettingsOpen && (
				<div className="fixed inset-0 z-[1300] flex items-center justify-center">
					<div
						className="absolute inset-0 bg-black/20"
						onClick={() => setIsSettingsOpen(false)}
					/>
					<div className="relative z-10 bg-background rounded-lg shadow-lg p-6 max-w-md w-full mx-4 border">
						<div className="flex items-center justify-between mb-4">
							<h2 className="text-lg font-semibold">{t('settings.title')}</h2>
							<Button
								size="icon"
								variant="ghost"
								onClick={() => setIsSettingsOpen(false)}
								aria-label={t('common.close')}
							>
								<X className="h-4 w-4" />
							</Button>
						</div>
						<div className="space-y-4">
							<div className="space-y-3">
								<div className="flex items-center justify-between">
									<label className="text-sm font-medium">
										{t('settings.theme')}
									</label>
									<select className="px-3 py-1 border rounded-md text-sm">
										<option value="system">{t('settings.theme_system')}</option>
										<option value="light">{t('settings.theme_light')}</option>
										<option value="dark">{t('settings.theme_dark')}</option>
									</select>
								</div>
								<div className="flex items-center justify-between">
									<label className="text-sm font-medium">
										{t('settings.language')}
									</label>
									<select className="px-3 py-1 border rounded-md text-sm">
										<option value="en">English</option>
										<option value="vi">Tiếng Việt</option>
									</select>
								</div>
							</div>
							<div className="flex justify-end gap-2 pt-4 border-t">
								<Button
									variant="outline"
									onClick={() => setIsSettingsOpen(false)}
								>
									{t('common.cancel')}
								</Button>
								<Button onClick={() => setIsSettingsOpen(false)}>
									{t('common.save')}
								</Button>
							</div>
						</div>
					</div>
				</div>
			)}

			{/* Feedback Modal */}
			{isFeedbackOpen && (
				<div className="fixed inset-0 z-[1300] flex items-center justify-center p-4">
					<div
						className="absolute inset-0 bg-black/20"
						onClick={() => setIsFeedbackOpen(false)}
					/>
					<div className="relative z-10 bg-background rounded-lg shadow-lg p-6 max-w-md w-full border">
						<div className="flex items-center justify-between mb-4">
							<h2 className="text-lg font-semibold">{t('feedback.title')}</h2>
							<Button
								size="icon"
								variant="ghost"
								onClick={() => setIsFeedbackOpen(false)}
								aria-label={t('common.close')}
							>
								<X className="h-4 w-4" />
							</Button>
						</div>
						<div className="space-y-4">
							<div>
								<label className="block text-sm font-medium mb-2">
									{t('feedback.message_label')}
								</label>
								<textarea
									className="w-full p-3 border rounded-md resize-none focus:ring-2 focus:ring-primary focus:border-transparent"
									rows={4}
									placeholder={t('feedback.message_placeholder')}
								/>
							</div>
							<div className="flex justify-end gap-2 pt-4 border-t">
								<Button
									variant="outline"
									onClick={() => setIsFeedbackOpen(false)}
								>
									Cancel
								</Button>
								<Button onClick={() => setIsFeedbackOpen(false)}>
									Send
								</Button>
							</div>
						</div>
					</div>
				</div>
			)}
		</>
	);
}
